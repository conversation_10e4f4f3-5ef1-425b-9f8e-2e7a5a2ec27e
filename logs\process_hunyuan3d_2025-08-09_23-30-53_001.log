========================================
AIStudio Real-time Log: hunyuan3d
Started: 2025-08-10T04:30:53.438Z
File: process_hunyuan3d_2025-08-09_23-30-53_001.log
========================================

[2025-08-10T04:30:53.437Z] [STDOUT] Using AIStudio models directory: M:\AIStudio\models
[2025-08-10T04:30:53.474Z] [STDOUT] F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.
[2025-08-10T04:30:53.474Z] [STDOUT] "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe"
[2025-08-10T04:30:53.475Z] [STDOUT] "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\get-pip.py"
[2025-08-10T04:30:54.314Z] [STDOUT] Creating virtual environment using portable Python...
[2025-08-10T04:30:54.315Z] [STDOUT] "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe" -m virtualenv "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv"
[2025-08-10T04:30:56.408Z] [STDOUT] sitecustomize.py applied
created virtual environment CPython3.11.9.final.0-64 in 1573ms
  creator CPython3Windows(dest=F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv, clear=False, no_vcs_ignore=False, global=False)
  seeder FromAppData(download=False, pip=bundle, setuptools=bundle, via=copy, app_data_dir=C:\Users\<USER>\AppData\Local\pypa\virtualenv)
    added seed packages: Deprecated==1.2.18, MarkupSafe==2.1.5, PyMatting==1.1.13, PyYAML==6.0.2, accelerate==1.5.2, aiofiles==23.2.1, annotated_types==0.7.0, antlr4_python3_runtime==4.9.3, anyio==4.9.0, attrs==25.3.0, certifi==2022.12.7, charset_normalizer==2.1.1, click==8.1.8, colorama==0.4.6, coloredlogs==15.0.1, contourpy==1.3.1, custom_rasterizer==0.1, cycler==0.12.1, dataclasses_json==0.6.7, diffusers==0.32.2, einops==0.8.1, fastapi==0.115.6, ffmpy==0.5.0, filelock==3.13.1, flatbuffers==25.2.10, fonttools==4.56.0, fsspec==2024.6.1, gradio==4.44.1, gradio_client==1.3.0, gradio_litmodel3d==0.0.1, groovy==0.1.2, h11==0.14.0, httpcore==1.0.7, httpx==0.28.1, huggingface_hub==0.29.3, humanfriendly==10.0, idna==3.4, imageio==2.37.0, importlib_metadata==8.6.1, importlib_resources==6.5.2, jinja2==3.1.4, jsonschema==4.23.0, jsonschema_specifications==2024.10.1, kiwisolver==1.4.8, lazy_loader==0.4, llvmlite==0.44.0, markdown_it_py==3.0.0, marshmallow==3.26.1, matplotlib==3.10.1, mdurl==0.1.2, mesh_processor==0.1.0, mpmath==1.3.0, msvc_runtime==14.42.34433, mypy_extensions==1.0.0, networkx==3.3, ninja==********, numba==0.61.0, numpy==1.25.2, omegaconf==2.3.0, onnxruntime==1.21.0, opencv_python==*********, opencv_python_headless==*********, orjson==3.10.16, packaging==24.2, pandas==2.2.3, pillow==10.4.0, pip==25.1.1, platformdirs==4.3.7, pooch==1.8.2, protobuf==6.30.2, psutil==7.0.0, pybind11==2.13.6, pydantic==2.10.5, pydantic_core==2.27.2, pydub==0.25.1, pygltflib==1.16.3, pygments==2.19.1, pymeshlab==2023.12.post3, pyparsing==3.2.3, pyreadline3==3.5.4, python_dateutil==2.9.0.post0, python_multipart==0.0.20, pytz==2025.2, referencing==0.36.2, regex==2024.11.6, rembg==2.0.65, requests==2.32.3, rich==13.9.4, rpds_py==0.24.0, ruff==0.11.2, safehttpx==0.1.6, safetensors==0.5.3, scikit_image==0.25.2, scipy==1.15.2, semantic_version==2.10.0, setuptools==80.9.0, shellingham==1.5.4, six==1.17.0, sniffio==1.3.1, starlette==0.41.3, sympy==1.13.1, tifffile==2025.3.13, tokenizers==0.21.1, tomlkit==0.12.0, torch==2.5.1+cu124, torchaudio==2.5.1+cu124, torchvision==0.20.1+cu124, tqdm==4.67.1, transformers==4.50.3, trimesh==4.6.5, typer==0.15.2, typing_extensions==4.12.2, typing_inspect==0.9.0, typing_inspection==0.4.0, tzdata==2025.2, urllib3==2.3.0, uvicorn==0.34.0, websockets==12.0, wrapt==1.17.2, xatlas==0.0.9, zipp==3.21.0
  activators BashActivator,BatchActivator,FishActivator,NushellActivator,PowerShellActivator,PythonActivator
[2025-08-10T04:30:56.465Z] [STDOUT] 1 file(s) copied.
[2025-08-10T04:30:56.675Z] [STDOUT] Portable Python located at: F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe
[2025-08-10T04:30:56.675Z] [STDOUT] Virtual environment Python set to: "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-10T04:30:56.676Z] [STDOUT] _
[2025-08-10T04:30:56.677Z] [STDOUT] Current Python: "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-10T04:30:56.677Z] [STDOUT] Virtual Env: F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-10T04:30:56.678Z] [STDOUT] _
[2025-08-10T04:30:56.683Z] [STDOUT] Current Python: "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-10T04:30:56.684Z] [STDOUT] Virtual Env: F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-10T04:30:56.684Z] [STDOUT] Starting the server, please wait...
[2025-08-10T04:31:13.677Z] [STDERR] F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv\Lib\site-packages\transformers\utils\hub.py:105: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
[2025-08-10T04:31:16.770Z] [STDOUT] Loading example img list ...
Loading example txt list ...
Loading example mv list ...
[2025-08-10T04:31:16.770Z] [STDERR] Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 12554.90it/s]
[2025-08-10T04:31:16.878Z] [STDERR] Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
[2025-08-10T04:31:16.881Z] [STDERR] Fetching 17 files: 100%|##########| 17/17 [00:00<00:00, 17214.67it/s]
[2025-08-10T04:31:16.968Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-10T04:31:18.675Z] [STDERR] Loading pipeline components...:  17%|#6        | 1/6 [00:01<00:08,  1.71s/it]
[2025-08-10T04:31:18.861Z] [STDERR] Loading pipeline components...:  50%|#####     | 3/6 [00:01<00:01,  1.96it/s]
[2025-08-10T04:31:19.172Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:02<00:00,  2.72it/s]
[2025-08-10T04:31:21.284Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-10T04:31:36.677Z] [STDERR] Loading pipeline components...:  17%|#6        | 1/6 [00:15<01:16, 15.39s/it]
[2025-08-10T04:31:36.838Z] [STDERR] Loading pipeline components...:  33%|###3      | 2/6 [00:15<00:25,  6.43s/it]
[2025-08-10T04:31:37.481Z] [STDERR] Loading pipeline components...:  50%|#####     | 3/6 [00:16<00:11,  3.79s/it]
[2025-08-10T04:31:39.487Z] [STDERR] Loading pipeline components...:  67%|######6   | 4/6 [00:18<00:06,  3.09s/it]
[2025-08-10T04:31:39.492Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:18<00:00,  3.03s/it]
[2025-08-10T04:31:50.716Z] [STDERR] 2025-08-09 23:31:50,716 - hy3dgen.shapgen - INFO - Try to load model from local path: M:\AIStudio\models\tencent/Hunyuan3D-2\hunyuan3d-dit-v2-0
[2025-08-10T04:31:50.716Z] [STDERR] 2025-08-09 23:31:50,716 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface
[2025-08-10T04:31:50.825Z] [STDERR] Fetching 6 files:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-10T04:31:50.826Z] [STDERR] Fetching 6 files: 100%|##########| 6/6 [00:00<00:00, 5962.05it/s]
[2025-08-10T04:31:50.835Z] [STDERR] 2025-08-09 23:31:50,835 - hy3dgen.shapgen - INFO - Loading model from M:\AIStudio\models\models--tencent--Hunyuan3D-2\snapshots\ac9fd33683d032aedf4f5e95efd204c5aac700d6\hunyuan3d-dit-v2-0\model.fp16.safetensors
[2025-08-10T04:32:39.724Z] [STDERR] INFO:     Started server process [29144]
INFO:     Waiting for application startup.
[2025-08-10T04:32:39.725Z] [STDERR] INFO:     Application startup complete.
[2025-08-10T04:32:39.736Z] [STDERR] INFO:     Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
[2025-08-10T04:32:39.778Z] [STDOUT] After launched, open a browser and enter 0.0.0.0:8080 into url, as if it was a website:
INFO:     127.0.0.1:56002 - "GET / HTTP/1.1" 200 OK
[2025-08-10T04:32:40.029Z] [STDOUT] INFO:     127.0.0.1:56002 - "POST /upload HTTP/1.1" 200 OK
[2025-08-10T04:32:45.587Z] [STDOUT] Created new folder: gradio_cache\18d19cee-c6a4-413d-bf15-e8e821af5cff
[2025-08-10T04:32:45.588Z] [STDERR] Diffusion Sampling::   0%|          | 0/25 [00:00<?, ?it/s]
[2025-08-10T04:32:50.573Z] [STDERR] Diffusion Sampling::  20%|##        | 5/25 [00:04<00:21,  1.07s/it]
[2025-08-10T04:32:56.636Z] [STDERR] Diffusion Sampling::  40%|####      | 10/25 [00:11<00:17,  1.19s/it]
[2025-08-10T04:33:02.696Z] [STDERR] Diffusion Sampling::  60%|######    | 15/25 [00:17<00:12,  1.21s/it]
[2025-08-10T04:33:08.767Z] [STDERR] Diffusion Sampling::  80%|########  | 20/25 [00:23<00:06,  1.21s/it]
[2025-08-10T04:33:14.846Z] [STDERR] Diffusion Sampling:: 100%|##########| 25/25 [00:29<00:00,  1.22s/it]
Diffusion Sampling:: 100%|##########| 25/25 [00:29<00:00,  1.17s/it]
[2025-08-10T04:33:16.136Z] [STDERR] Volume Decoding:   0%|          | 0/565820 [00:00<?, ?it/s]
[2025-08-10T04:33:55.883Z] [STDERR] Volume Decoding:   5%|4         | 25503/565820 [00:39<13:49, 651.38it/s]
[2025-08-10T04:34:39.721Z] [STDERR] Volume Decoding:  10%|9         | 53788/565820 [01:23<13:05, 651.73it/s]
[2025-08-10T04:35:23.290Z] [STDERR] Volume Decoding:  15%|#4        | 82066/565820 [02:07<12:26, 648.01it/s]
[2025-08-10T04:36:07.635Z] [STDERR] Volume Decoding:  20%|#9        | 110374/565820 [02:51<11:35, 654.64it/s]
[2025-08-10T04:36:51.566Z] [STDERR] Volume Decoding:  25%|##4       | 138681/565820 [03:35<11:13, 634.24it/s]
[2025-08-10T04:37:34.815Z] [STDERR] Volume Decoding:  30%|##9       | 166969/565820 [04:18<08:49, 753.18it/s]
[2025-08-10T04:38:16.973Z] [STDERR] Volume Decoding:  35%|###4      | 195210/565820 [05:00<10:00, 616.89it/s]
[2025-08-10T04:39:01.276Z] [STDERR] Volume Decoding:  40%|###9      | 223533/565820 [05:45<08:47, 649.03it/s]
[2025-08-10T04:39:45.215Z] [STDERR] Volume Decoding:  45%|####4     | 251825/565820 [06:29<07:58, 656.15it/s]
[2025-08-10T04:40:29.905Z] [STDERR] Volume Decoding:  50%|####9     | 280125/565820 [07:13<07:18, 652.09it/s]
[2025-08-10T04:41:13.724Z] [STDERR] Volume Decoding:  55%|#####4    | 308391/565820 [07:57<06:36, 649.27it/s]
[2025-08-10T04:41:57.610Z] [STDERR] Volume Decoding:  60%|#####9    | 336676/565820 [08:41<06:05, 626.86it/s]
[2025-08-10T04:42:41.694Z] [STDERR] Volume Decoding:  65%|######4   | 364973/565820 [09:25<05:04, 659.81it/s]
[2025-08-10T04:43:25.578Z] [STDERR] Volume Decoding:  70%|######9   | 393285/565820 [10:09<04:22, 658.53it/s]
[2025-08-10T04:44:08.981Z] [STDERR] Volume Decoding:  75%|#######4  | 421567/565820 [10:52<03:38, 659.26it/s]
[2025-08-10T04:44:52.465Z] [STDERR] Volume Decoding:  80%|#######9  | 449862/565820 [11:36<02:56, 658.83it/s]
[2025-08-10T04:45:35.758Z] [STDERR] Volume Decoding:  85%|########4 | 478148/565820 [12:19<02:15, 648.86it/s]
[2025-08-10T04:46:19.989Z] [STDERR] Volume Decoding:  90%|########9 | 506452/565820 [13:03<01:32, 642.58it/s]
[2025-08-10T04:47:03.746Z] [STDERR] Volume Decoding:  95%|#########4| 534737/565820 [13:47<00:49, 625.50it/s]
[2025-08-10T04:47:47.591Z] [STDERR] Volume Decoding: 100%|#########9| 563036/565820 [14:31<00:04, 644.70it/s]
[2025-08-10T04:47:52.003Z] [STDERR] Volume Decoding: 100%|##########| 565820/565820 [14:35<00:00, 646.01it/s]
[2025-08-10T04:47:54.390Z] [STDERR] 2025-08-09 23:47:54,390 - hy3dgen.shapgen - INFO - ---Shape generation takes 912.5892059803009 seconds ---
[2025-08-10T04:47:57.818Z] [STDERR] 2025-08-09 23:47:57,818 - hy3dgen.shapgen - INFO - ---Postprocessing takes 2.9332804679870605 seconds ---
[2025-08-10T04:48:13.662Z] [STDERR] 2025-08-09 23:48:13,662 - hy3dgen.shapgen - INFO - ---Face Reduction takes 15.843966484069824 seconds ---
[2025-08-10T04:49:26.399Z] [STDERR] 2025-08-09 23:49:26,399 - hy3dgen.shapgen - INFO - ---Texture Generation takes 72.73734188079834 seconds ---
[2025-08-10T04:49:28.173Z] [STDOUT] Find html file gradio_cache\18d19cee-c6a4-413d-bf15-e8e821af5cff\textured_mesh.html, True, relative HTML path is /static/18d19cee-c6a4-413d-bf15-e8e821af5cff\textured_mesh.html
INFO:     127.0.0.1:56003 - "POST /api/predict HTTP/1.1" 200 OK
[2025-08-10T04:49:28.212Z] [STDOUT] INFO:     127.0.0.1:56116 - "GET /file%3DC%3A/Users/<USER>/AppData/Local/Temp/gradio/6b237d69170c37a1510a6ca20336eb31cb3e27d94331f147f185fb661124133c/textured_mesh.glb HTTP/1.1" 200 OK
[2025-08-10T04:49:34.409Z] [STDOUT] Something went wrong, consider removing code/hunyuan_init_done.txt and the venv folder to re-initialize from scratch
[2025-08-10T04:49:34.410Z] [STDOUT] Press any key to continue . . .
