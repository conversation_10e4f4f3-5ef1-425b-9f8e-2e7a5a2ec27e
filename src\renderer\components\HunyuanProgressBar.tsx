import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>cle, Circle, Loader2, Clock, Cpu, Download, ChevronDown, ChevronUp, Sparkles } from 'lucide-react';

interface ProgressStage {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  status: 'pending' | 'active' | 'completed' | 'error';
  estimatedTime?: string;
}

interface HunyuanProgressBarProps {
  isVisible: boolean;
  isDarkMode: boolean;
  sessionId?: string;
  delighterEnabled?: boolean;
}

const getHunyuanStages = (includeDelighter: boolean = false): ProgressStage[] => {
  const stages: ProgressStage[] = [
    {
      id: 'hunyuan_loading',
      name: 'Loading Pipeline',
      description: 'Initializing Hunyuan3D pipeline and models',
      icon: <Circle className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime: '30s'
    },
    {
      id: 'hunyuan_diffusion',
      name: 'Diffusion Sampling',
      description: 'Generating 3D structure through diffusion process',
      icon: <Cpu className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime: '300s'
    },
    {
      id: 'hunyuan_decoding',
      name: 'Volume Decoding',
      description: 'Converting diffusion output to 3D volume',
      icon: <Cpu className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime: '120s'
    },
    {
      id: 'hunyuan_texture',
      name: 'Texturing Mesh',
      description: 'Processing mesh and applying textures',
      icon: <Download className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime: '180s'
    }
  ];
  if (includeDelighter) {
    stages.push({
      id: 'texture_enhancement',
      name: 'Texture Enhancement',
      description: 'Enhancing textures with Agisoft De-Lighter',
      icon: <Sparkles className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime: '20s' // was 10s
    });
  }
  return stages;
};

export const HunyuanProgressBar: React.FC<HunyuanProgressBarProps> = ({
  isVisible,
  isDarkMode,
  sessionId,
  delighterEnabled = false
}) => {
  const [stages, setStages] = useState<ProgressStage[]>(getHunyuanStages(delighterEnabled));
  const [currentStageIndex, setCurrentStageIndex] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const lastSessionIdRef = React.useRef<string | undefined>(undefined);

  // Reset stage state when a new sessionId starts
  useEffect(() => {
    if (!isVisible || !sessionId) return;
    if (lastSessionIdRef.current === sessionId) return; // Already started for this session
    lastSessionIdRef.current = sessionId;
    setOverallProgress(0);
    setElapsedTime(0);
    setEstimatedTimeRemaining('');
    setStages(getHunyuanStages(delighterEnabled).map(stage => ({
      ...stage,
      progress: 0,
      status: 'pending' as const
    })));
    setCurrentStageIndex(0);
  }, [isVisible, sessionId, delighterEnabled]);

  // Timer for elapsed time
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isVisible && currentStageIndex < stages.length) {
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isVisible, currentStageIndex, stages.length]);

  // Calculate overall progress based on stage weights
  useEffect(() => {
    // Define stage weights (total should equal 100)
    const stageWeights = {
      'hunyuan_loading': 10,      // 10% of total
      'hunyuan_diffusion': 40,    // 40% of total
      'hunyuan_decoding': 30,     // 30% of total
      'hunyuan_texture': 20       // 20% of total
    };

    let totalProgress = 0;

    stages.forEach(stage => {
      const weight = stageWeights[stage.id] || (100 / stages.length);
      if (stage.status === 'completed') {
        // Completed stages contribute their full weight
        totalProgress += weight;
      } else if (stage.status === 'active') {
        // Active stage contributes partial weight based on progress
        totalProgress += (stage.progress / 100) * weight;
      }
      // Pending stages contribute 0
    });

    // Ensure progress never goes backwards and caps at 100
    setOverallProgress(prev => Math.min(Math.max(totalProgress, prev), 100));
  }, [stages]);

  // Calculate estimated time remaining
  useEffect(() => {
    const remainingStages = stages.slice(currentStageIndex);
    const totalEstimated = remainingStages.reduce((total, stage) => {
      const time = parseInt(stage.estimatedTime?.replace('s', '') || '0');
      return total + time;
    }, 0);
    const currentStageProgress = stages[currentStageIndex]?.progress || 0;
    const currentStageTime = parseInt(stages[currentStageIndex]?.estimatedTime?.replace('s', '') || '0');
    const currentStageRemaining = (currentStageTime * (100 - currentStageProgress)) / 100;
    const totalRemaining = Math.max(0, totalEstimated - currentStageTime + currentStageRemaining);
    setEstimatedTimeRemaining(totalRemaining > 0 ? `${Math.ceil(totalRemaining)}s` : '');
  // Listen for real progress events from backend
  useEffect(() => {
    if (!isVisible || !sessionId) return;
    const removeListener = (window as any).electronAPI?.onPipelineStatus?.((status: any) => {
      if (!status || typeof status !== 'object') return;
      if (status.event !== 'progress') return;
      if (status.session_id !== sessionId) return;
      if (!status.stage?.startsWith('hunyuan')) return;

      setStages(prev => {
        const currentStageIdx = prev.findIndex(s => s.id === status.stage);

        return prev.map((stage, index) => {
          if (stage.id === status.stage) {
            const progress = Math.min(100, status.stage_progress || status.progress || 0);
            const newStatus = progress >= 100 ? 'completed' : (progress > 0 ? 'active' : 'pending');

            // Update current stage index when a stage becomes active
            if (newStatus === 'active' && stage.status !== 'active') {
              setCurrentStageIndex(index);
            }

            return {
              ...stage,
              progress,
              status: newStatus,
              description: status.message || stage.description
            };
          }

          // Mark all previous stages as completed when a later stage is active
          if (currentStageIdx > index && currentStageIdx !== -1) {
            return {
              ...stage,
              progress: 100,
              status: 'completed'
            };
          }

          // Keep future stages as pending
          if (currentStageIdx < index && currentStageIdx !== -1) {
            return {
              ...stage,
              progress: 0,
              status: 'pending'
            };
          }

          return stage;
        });
      });

      if (status.overall_progress !== undefined) {
        setOverallProgress(Math.min(100, status.overall_progress));
      }
    });
    return () => { if (removeListener) removeListener(); };
  }, [isVisible, sessionId]);
  }, [stages, currentStageIndex]);

  // Remove simulation – rely on real progress events from backend
  useEffect(() => {
    return () => {};
  }, [isVisible, sessionId, delighterEnabled]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
  };

  if (!isVisible) return null;

  const currentStage = stages[currentStageIndex];
  const currentStageName = currentStage?.name || 'Initializing...';

  return (
    <div className={`relative w-full rounded-lg border ${
      isDarkMode
        ? 'bg-gray-800 border-gray-700'
        : 'bg-white border-gray-200'
    } shadow-lg overflow-visible`}>
      {/* Compact Header - Always Visible */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Loader2 className={`w-5 h-5 animate-spin ${
              isDarkMode ? 'text-blue-400' : 'text-blue-600'
            }`} />
            <div className="flex flex-col">
              <h3 className={`text-sm font-semibold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Generating 3D Model
              </h3>
              <p className={`text-xs ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                {currentStageName}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-4 text-xs">
              <div className={`flex items-center gap-1 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                <Clock className="w-3 h-3" />
                <span>{formatTime(elapsedTime)}</span>
              </div>
              <span className={`font-medium ${
                isDarkMode ? 'text-blue-400' : 'text-blue-600'
              }`}>
                {Math.round(overallProgress)}%
              </span>
            </div>

            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className={`p-1 rounded hover:bg-opacity-20 transition-colors ${
                isDarkMode
                  ? 'text-gray-400 hover:bg-gray-600 hover:text-gray-300'
                  : 'text-gray-500 hover:bg-gray-200 hover:text-gray-700'
              }`}
              aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>

        {/* Compact Progress Bar */}
        <div className="mt-3">
          <div className={`w-full h-2 rounded-full ${
            isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
          }`}>
            <div
              className={`h-full rounded-full transition-all duration-300 ${
                isDarkMode
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600'
              }`}
              style={{ width: `${overallProgress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Expandable Details - Positioned absolutely to expand downward only */}
      {isExpanded && (
        <div className={`absolute top-full left-0 right-0 z-10 rounded-b-lg border-t-0 border ${
          isDarkMode
            ? 'bg-gray-800 border-gray-700'
            : 'bg-white border-gray-200'
        } shadow-lg`}>
          <div className={`border-t px-4 pb-4 ${
            isDarkMode ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="pt-4 space-y-3">
              {stages.map((stage, index) => (
                <div key={stage.id} className="flex items-center gap-3">
                  {/* Status Icon */}
                  <div className={`flex-shrink-0 ${
                    stage.status === 'completed' ? (isDarkMode ? 'text-green-400' : 'text-green-600') :
                    stage.status === 'active' ? (isDarkMode ? 'text-blue-400' : 'text-blue-600') :
                    stage.status === 'error' ? (isDarkMode ? 'text-red-400' : 'text-red-600') :
                    (isDarkMode ? 'text-gray-500' : 'text-gray-400')
                  }`}>
                    {stage.status === 'completed' ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : stage.status === 'active' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <div className="w-4 h-4 flex items-center justify-center">
                        {React.cloneElement(stage.icon as React.ReactElement, { className: 'w-3 h-3' })}
                      </div>
                    )}
                  </div>

                  {/* Stage Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className={`text-sm font-medium ${
                        stage.status === 'active' ? (isDarkMode ? 'text-white' : 'text-gray-900') :
                        stage.status === 'completed' ? (isDarkMode ? 'text-green-400' : 'text-green-600') :
                        (isDarkMode ? 'text-gray-400' : 'text-gray-500')
                      }`}>
                        {stage.name}
                      </h4>
                      {stage.status === 'active' && (
                        <span className={`text-xs font-medium ${
                          isDarkMode ? 'text-blue-400' : 'text-blue-600'
                        }`}>
                          {Math.round(stage.progress)}%
                        </span>
                      )}
                    </div>
                    <p className={`text-xs ${
                      isDarkMode ? 'text-gray-500' : 'text-gray-400'
                    }`}>
                      {stage.description}
                    </p>

                    {/* Stage Progress Bar */}
                    {(stage.status === 'active' || stage.status === 'completed') && (
                      <div className={`mt-2 w-full h-1.5 rounded-full ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      }`}>
                        <div
                          className={`h-full rounded-full transition-all duration-200 ${
                            stage.status === 'completed'
                              ? (isDarkMode ? 'bg-green-500' : 'bg-green-600')
                              : (isDarkMode ? 'bg-blue-500' : 'bg-blue-600')
                          }`}
                          style={{ width: `${stage.status === 'completed' ? 100 : stage.progress}%` }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {estimatedTimeRemaining && (
              <div className={`mt-4 pt-3 border-t text-center text-xs ${
                isDarkMode ? 'border-gray-700 text-gray-400' : 'border-gray-200 text-gray-500'
              }`}>
                Estimated time remaining: {estimatedTimeRemaining}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};