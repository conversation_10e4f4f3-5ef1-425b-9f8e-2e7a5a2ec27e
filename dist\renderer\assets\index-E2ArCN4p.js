var U=new TextDecoder,s=(e,t=0,r=e.length)=>U.decode(e.slice(t,r)),m=(e,t=0,r=e.length)=>e.slice(t,r).reduce((i,n)=>i+`0${n.toString(16)}`.slice(-2),""),v=(e,t)=>new DataView(e.buffer,e.byteOffset+t),P=(e,t=0)=>v(e,t).getInt16(0,!0),I=(e,t=0)=>v(e,t).getUint16(0,!1),f=(e,t=0)=>v(e,t).getUint16(0,!0),B=(e,t=0)=>{const r=v(e,t);return r.getUint16(0,!0)+(r.getUint8(2)<<16)},j=(e,t=0)=>v(e,t).getInt32(0,!0),c=(e,t=0)=>v(e,t).getUint32(0,!1),w=(e,t=0)=>v(e,t).getUint32(0,!0),x=(e,t,r)=>v(e,t).getBigUint64(0,!r),V={readUInt16BE:I,readUInt16LE:f,readUInt32BE:c,readUInt32LE:w};function h(e,t,r=0,i=!1){const a=`readUInt${t}${i?"BE":"LE"}`;return V[a](e,r)}function X(e,t){if(e.length-t<4)return;const r=c(e,t);if(!(e.length-t<r))return{name:s(e,4+t,8+t),offset:t,size:r}}function g(e,t,r){for(;r<e.length;){const i=X(e,r);if(!i)break;if(i.name===t)return i;r+=i.size>0?i.size:8}}var Z={validate:e=>s(e,0,2)==="BM",calculate:e=>({height:Math.abs(j(e,22)),width:w(e,18)})},J=1,W=6,$=16;function R(e,t){const r=e[t];return r===0?256:r}function _(e,t){const r=W+t*$;return{height:R(e,r+1),width:R(e,r)}}var F={validate(e){const t=f(e,0),r=f(e,4);return t!==0||r===0?!1:f(e,2)===J},calculate(e){const t=f(e,4),r=_(e,0);if(t===1)return r;const i=[];for(let n=0;n<t;n+=1)i.push(_(e,n));return{width:r.width,height:r.height,images:i}}},K=2,q={validate(e){const t=f(e,0),r=f(e,4);return t!==0||r===0?!1:f(e,2)===K},calculate:e=>F.calculate(e)},Q={validate:e=>w(e,0)===542327876,calculate:e=>({height:w(e,12),width:w(e,16)})},ee=/^GIF8[79]a/,te={validate:e=>ee.test(s(e,0,6)),calculate:e=>({height:f(e,8),width:f(e,6)})},re={avif:"avif",mif1:"heif",msf1:"heif",heic:"heic",heix:"heic",hevc:"heic",hevx:"heic"},ie={validate(e){if(s(e,4,8)!=="ftyp")return!1;const r=g(e,"ftyp",0);return r?s(e,r.offset+8,r.offset+12)in re:!1},calculate(e){const t=g(e,"meta",0),r=t&&g(e,"iprp",t.offset+12),i=r&&g(e,"ipco",r.offset+8);if(!i)throw new TypeError("Invalid HEIF, no ipco box found");const n=s(e,8,12),a=[];let o=i.offset+8;for(;o<i.offset+i.size;){const d=g(e,"ispe",o);if(!d)break;const u=c(e,d.offset+12),O=c(e,d.offset+16),E=g(e,"clap",o);let p=u,Y=O;if(E&&E.offset<i.offset+i.size){const k=c(e,E.offset+12);p=u-k}a.push({height:Y,width:p}),o=d.offset+d.size}if(a.length===0)throw new TypeError("Invalid HEIF, no sizes found");return{width:a[0].width,height:a[0].height,type:n,...a.length>1?{images:a}:{}}}},ne=8,ae=4,se=4,oe={ICON:32,"ICN#":32,"icm#":16,icm4:16,icm8:16,"ics#":16,ics4:16,ics8:16,is32:16,s8mk:16,icp4:16,icl4:32,icl8:32,il32:32,l8mk:32,icp5:32,ic11:32,ich4:48,ich8:48,ih32:48,h8mk:48,icp6:64,ic12:32,it32:128,t8mk:128,ic07:128,ic08:256,ic13:256,ic09:512,ic14:512,ic10:1024};function ce(e,t){const r=t+se;return[s(e,t,r),c(e,r)]}function he(e){const t=oe[e];return{width:t,height:t,type:e}}var fe={validate:e=>s(e,0,4)==="icns",calculate(e){const t=e.length,r=c(e,ae);let i=ne;const n=[];for(;i<r&&i<t;){const a=ce(e,i),o=he(a[0]);n.push(o),i+=a[1]}if(n.length===0)throw new TypeError("Invalid ICNS, no sizes found");return{width:n[0].width,height:n[0].height,...n.length>1?{images:n}:{}}}},le={validate:e=>c(e,0)===4283432785,calculate:e=>({height:c(e,12),width:c(e,8)})},de={validate(e){if(s(e,4,8)!=="jP  ")return!1;const r=g(e,"ftyp",0);return r?s(e,r.offset+8,r.offset+12)==="jp2 ":!1},calculate(e){const t=g(e,"jp2h",0),r=t&&g(e,"ihdr",t.offset+8);if(r)return{height:c(e,r.offset+8),width:c(e,r.offset+12)};throw new TypeError("Unsupported JPEG 2000 format")}},ge="45786966",ue=2,S=6,ve=2,we="4d4d",me="4949",G=12,Ee=2;function Te(e){return m(e,2,6)===ge}function Ie(e,t){return{height:I(e,t),width:I(e,t+2)}}function be(e,t){const i=S+8,n=h(e,16,i,t);for(let a=0;a<n;a++){const o=i+Ee+a*G,d=o+G;if(o>e.length)return;const u=e.slice(o,d);if(h(u,16,0,t)===274)return h(u,16,2,t)!==3||h(u,32,4,t)!==1?void 0:h(u,16,8,t)}}function ye(e,t){const r=e.slice(ue,t),i=m(r,S,S+ve),n=i===we;if(n||i===me)return be(r,n)}function pe(e,t){if(t>e.length)throw new TypeError("Corrupt JPG, exceeded buffer limits")}var Ne={validate:e=>m(e,0,2)==="ffd8",calculate(e){let t=e.slice(4),r,i;for(;t.length;){const n=I(t,0);if(pe(t,n),t[n]!==255){t=t.slice(1);continue}if(Te(t)&&(r=ye(t,n)),i=t[n+1],i===192||i===193||i===194){const a=Ie(t,n+5);return r?{height:a.height,orientation:r,width:a.width}:a}t=t.slice(n+2)}throw new TypeError("Invalid JPG, no size found")}},Se=class{constructor(e,t){this.input=e,this.endianness=t,this.byteOffset=2,this.bitOffset=0}getBits(e=1){let t=0,r=0;for(;r<e;){if(this.byteOffset>=this.input.length)throw new Error("Reached end of input");const i=this.input[this.byteOffset],n=8-this.bitOffset,a=Math.min(e-r,n);if(this.endianness==="little-endian"){const o=(1<<a)-1,d=i>>this.bitOffset&o;t|=d<<r}else{const o=(1<<a)-1<<8-this.bitOffset-a,d=(i&o)>>8-this.bitOffset-a;t=t<<a|d}r+=a,this.bitOffset+=a,this.bitOffset===8&&(this.byteOffset++,this.bitOffset=0)}return t}};function H(e,t){if(t)return 8*(1+e.getBits(5));const r=e.getBits(2),i=[9,13,18,30][r];return 1+e.getBits(i)}function xe(e,t,r,i){if(t&&r===0)return 8*(1+e.getBits(5));if(r===0)return H(e,!1);const n=[1,1.2,4/3,1.5,16/9,5/4,2];return Math.floor(i*n[r-1])}var z={validate:e=>m(e,0,2)==="ff0a",calculate(e){const t=new Se(e,"little-endian"),r=t.getBits(1)===1,i=H(t,r),n=t.getBits(3);return{width:xe(t,r,n,i),height:i}}};function Oe(e){const t=g(e,"jxlc",0);if(t)return e.slice(t.offset+8,t.offset+t.size);const r=Pe(e);if(r.length>0)return Be(r)}function Pe(e){const t=[];let r=0;for(;r<e.length;){const i=g(e,"jxlp",r);if(!i)break;t.push(e.slice(i.offset+12,i.offset+i.size)),r=i.offset+i.size}return t}function Be(e){const t=e.reduce((n,a)=>n+a.length,0),r=new Uint8Array(t);let i=0;for(const n of e)r.set(n,i),i+=n.length;return r}var Re={validate:e=>{if(s(e,4,8)!=="JXL ")return!1;const r=g(e,"ftyp",0);return r?s(e,r.offset+8,r.offset+12)==="jxl ":!1},calculate(e){const t=Oe(e);if(t)return z.calculate(t);throw new Error("No codestream found in JXL container")}},_e={validate:e=>{const t=s(e,1,7);return["KTX 11","KTX 20"].includes(t)},calculate:e=>{const t=e[5]===49?"ktx":"ktx2",r=t==="ktx"?36:20;return{height:w(e,r+4),width:w(e,r),type:t}}},Ge=`PNG\r

`,Ae="IHDR",A="CgBI",Le={validate(e){if(Ge===s(e,1,8)){let t=s(e,12,16);if(t===A&&(t=s(e,28,32)),t!==Ae)throw new TypeError("Invalid PNG");return!0}return!1},calculate(e){return s(e,12,16)===A?{height:c(e,36),width:c(e,32)}:{height:c(e,20),width:c(e,16)}}},L={P1:"pbm/ascii",P2:"pgm/ascii",P3:"ppm/ascii",P4:"pbm",P5:"pgm",P6:"ppm",P7:"pam",PF:"pfm"},C={default:e=>{let t=[];for(;e.length>0;){const r=e.shift();if(r[0]!=="#"){t=r.split(" ");break}}if(t.length===2)return{height:Number.parseInt(t[1],10),width:Number.parseInt(t[0],10)};throw new TypeError("Invalid PNM")},pam:e=>{const t={};for(;e.length>0;){const r=e.shift();if(r.length>16||r.charCodeAt(0)>128)continue;const[i,n]=r.split(" ");if(i&&n&&(t[i.toLowerCase()]=Number.parseInt(n,10)),t.height&&t.width)break}if(t.height&&t.width)return{height:t.height,width:t.width};throw new TypeError("Invalid PAM")}},Ce={validate:e=>s(e,0,2)in L,calculate(e){const t=s(e,0,2),r=L[t],i=s(e,3).split(/[\r\n]+/);return(C[r]||C.default)(i)}},Fe={validate:e=>s(e,0,4)==="8BPS",calculate:e=>({height:c(e,14),width:c(e,18)})},D=/<svg\s([^>"']|"[^"]*"|'[^']*')*>/,T={height:/\sheight=(['"])([^%]+?)\1/,root:D,viewbox:/\sviewBox=(['"])(.+?)\1/i,width:/\swidth=(['"])([^%]+?)\1/},N=2.54,M={in:96,cm:96/N,em:16,ex:8,m:96/N*100,mm:96/N/10,pc:96/72/12,pt:96/72,px:1},He=new RegExp(`^([0-9.]+(?:e\\d+)?)(${Object.keys(M).join("|")})?$`);function b(e){const t=He.exec(e);if(t)return Math.round(Number(t[1])*(M[t[2]]||1))}function ze(e){const t=e.split(" ");return{height:b(t[3]),width:b(t[2])}}function De(e){const t=e.match(T.width),r=e.match(T.height),i=e.match(T.viewbox);return{height:r&&b(r[2]),viewbox:i&&ze(i[2]),width:t&&b(t[2])}}function Me(e){return{height:e.height,width:e.width}}function Ye(e,t){const r=t.width/t.height;return e.width?{height:Math.floor(e.width/r),width:e.width}:e.height?{height:e.height,width:Math.floor(e.height*r)}:{height:t.height,width:t.width}}var ke={validate:e=>D.test(s(e,0,1e3)),calculate(e){const t=s(e).match(T.root);if(t){const r=De(t[0]);if(r.width&&r.height)return Me(r);if(r.viewbox)return Ye(r,r.viewbox)}throw new TypeError("Invalid SVG")}},Ue={validate(e){return f(e,0)===0&&f(e,4)===0},calculate(e){return{height:f(e,14),width:f(e,12)}}},l={TAG:{WIDTH:256,HEIGHT:257,COMPRESSION:259},TYPE:{SHORT:3,LONG:4,LONG8:16},ENTRY_SIZE:{STANDARD:12,BIG:20},COUNT_SIZE:{STANDARD:2,BIG:8}};function je(e,{isBigEndian:t,isBigTiff:r}){const i=r?Number(x(e,8,t)):h(e,32,4,t),n=r?l.COUNT_SIZE.BIG:l.COUNT_SIZE.STANDARD;return e.slice(i+n)}function Ve(e,t,r,i){switch(t){case l.TYPE.SHORT:return h(e,16,r,i);case l.TYPE.LONG:return h(e,32,r,i);case l.TYPE.LONG8:{const n=Number(x(e,r,i));if(n>Number.MAX_SAFE_INTEGER)throw new TypeError("Value too large");return n}default:return 0}}function Xe(e,t){const r=t?l.ENTRY_SIZE.BIG:l.ENTRY_SIZE.STANDARD;if(e.length>r)return e.slice(r)}function Ze(e,{isBigEndian:t,isBigTiff:r}){const i={};let n=e;for(;n!=null&&n.length;){const a=h(n,16,0,t),o=h(n,16,2,t),d=r?Number(x(n,4,t)):h(n,32,4,t);if(a===0)break;if(d===1&&(o===l.TYPE.SHORT||o===l.TYPE.LONG||r&&o===l.TYPE.LONG8)){const u=r?12:8;i[a]=Ve(n,o,u,t)}n=Xe(n,r)}return i}function Je(e){const t=s(e,0,2),r=h(e,16,2,t==="MM");return{isBigEndian:t==="MM",isBigTiff:r===43}}function We(e,t){const r=h(e,16,4,t),i=h(e,16,6,t);if(r!==8||i!==0)throw new TypeError("Invalid BigTIFF header")}var $e=new Set(["49492a00","4d4d002a","49492b00","4d4d002b"]),Ke={validate:e=>{const t=m(e,0,4);return $e.has(t)},calculate(e){const t=Je(e);t.isBigTiff&&We(e,t.isBigEndian);const r=je(e,t),i=Ze(r,t),n={height:i[l.TAG.HEIGHT],width:i[l.TAG.WIDTH],type:t.isBigTiff?"bigtiff":"tiff"};if(i[l.TAG.COMPRESSION]&&(n.compression=i[l.TAG.COMPRESSION]),!n.width||!n.height)throw new TypeError("Invalid Tiff. Missing tags");return n}};function qe(e){return{height:1+B(e,7),width:1+B(e,4)}}function Qe(e){return{height:1+((e[4]&15)<<10|e[3]<<2|(e[2]&192)>>6),width:1+((e[2]&63)<<8|e[1])}}function et(e){return{height:P(e,8)&16383,width:P(e,6)&16383}}var tt={validate(e){const t=s(e,0,4)==="RIFF",r=s(e,8,12)==="WEBP",i=s(e,12,15)==="VP8";return t&&r&&i},calculate(e){const t=s(e,12,16),r=e.slice(20,30);if(t==="VP8X"){const n=r[0],a=(n&192)===0,o=(n&1)===0;if(a&&o)return qe(r);throw new TypeError("Invalid WebP")}if(t==="VP8 "&&r[0]!==47)return et(r);const i=m(r,3,6);if(t==="VP8L"&&i!=="9d012a")return Qe(r);throw new TypeError("Invalid WebP")}},y=new Map([["bmp",Z],["cur",q],["dds",Q],["gif",te],["heif",ie],["icns",fe],["ico",F],["j2c",le],["jp2",de],["jpg",Ne],["jxl",Re],["jxl-stream",z],["ktx",_e],["png",Le],["pnm",Ce],["psd",Fe],["svg",ke],["tga",Ue],["tiff",Ke],["webp",tt]]),rt=Array.from(y.keys()),it=new Map([[0,"heif"],[56,"psd"],[66,"bmp"],[68,"dds"],[71,"gif"],[73,"tiff"],[77,"tiff"],[82,"webp"],[105,"icns"],[137,"png"],[255,"jpg"]]);function nt(e){const t=e[0],r=it.get(t);return r&&y.get(r).validate(e)?r:rt.find(i=>y.get(i).validate(e))}var at={disabledTypes:[]};function st(e){const t=nt(e);if(typeof t<"u"){if(at.disabledTypes.indexOf(t)>-1)throw new TypeError(`disabled file type: ${t}`);const r=y.get(t).calculate(e);if(r!==void 0){if(r.type=r.type??t,r.images&&r.images.length>1){const i=r.images.reduce((n,a)=>a.width*a.height>n.width*n.height?a:n,r.images[0]);r.width=i.width,r.height=i.height}return r}}throw new TypeError(`unsupported file type: ${t}`)}export{st as default,st as imageSize,rt as types};
