from enum import Enum
from typing import Optional, Dict
from fastapi import Form
from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    PROCESSING = "PROCESSING"
    PREVIEW_READY = "PREVIEW_READY"
    COMPLETE = "COMPLETE"
    FAILED = "FAILED"


class GenerationArgForm:
    def __init__(
        self,
        seed: int = Form(1234),
        guidance_scale: float = Form(5.0),
        num_inference_steps: int = Form(20),
        octree_resolution: int = Form(256),
        num_chunks: int = Form(80),
        mesh_simplify_ratio: float = Form(0.1),
        apply_texture: bool = Form(True),
        texture_size: int = Form(2048),
        output_format: str = Form("glb"),
        # Advanced post-processing options (enabled by default)
        remove_floaters: bool = Form(True),
        smooth_normals: bool = Form(True),
        denoise_strength: float = Form(1.2),
        multi_view_sampling: bool = Form(True),
        flux_guidance: bool = Form(True),
        jit_acceleration: bool = Form(True),
        delight_toggle: bool = Form(True),
    ):
        self.seed = seed
        self.guidance_scale = guidance_scale
        self.num_inference_steps = num_inference_steps
        self.octree_resolution = octree_resolution
        self.num_chunks = num_chunks*1000
        self.mesh_simplify_ratio = mesh_simplify_ratio
        self.apply_texture = apply_texture
        self.texture_size = texture_size
        self.output_format = output_format
        # Advanced post-processing settings
        self.remove_floaters = remove_floaters
        self.smooth_normals = smooth_normals
        self.denoise_strength = denoise_strength
        self.multi_view_sampling = multi_view_sampling
        self.flux_guidance = flux_guidance
        self.jit_acceleration = jit_acceleration
        self.delight_toggle = delight_toggle


class GenerationResponse(BaseModel):
    # No task_id anymore, we focus on a single generation
    status: TaskStatus
    progress: int = 0
    message: str = ""
    # Only used if we did "generate_preview"
    preview_urls: Optional[Dict[str, str]] = None
    # Only used if generation is complete
    model_url: Optional[str] = None


class StatusResponse(BaseModel):
    status: TaskStatus
    progress: int
    message: str
    busy: bool