const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const FormData = require('form-data');
const chalk = require('chalk');
const logger = require('./logger');
const realtimeLogger = require('./realtimeLogger');
chalk.level = 3; // Force 16m color support for red logs

// Configuration constants - Updated for Hunyuan3D-StableProjectorz
const HUNYAUN_PORT = 8080; // Port configured for Gradio server (default port)
const HUNYAUN_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/v13_hunyuan2-stableprojectorz/run-browser_(slower)/run-gradio-full-singleview.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output/hunyuan3d');

// Connection attempt counter to reduce log spam
let connectionAttemptCounter = 0;
let recoveryAttemptCounter = 0;
const MAX_RECOVERY_ATTEMPTS = 0; // Disabled auto-recovery completely

// Track progress percentages to filter repetitive messages
let lastLoggedDiffusionPercent = -1;
let lastLoggedVolumePercent = -1;

// Convert Hunyuan3D statistics to ModelViewer format
function formatHunyuanStatsForModelViewer(hunyuanStats, glbFile, settings) {
  if (!hunyuanStats) return null;

  const modelViewerStats = {
    generationMode: 'image-to-3d',
    imageModel: 'Hunyuan3D-2.1',
    settings: {
      steps: hunyuanStats.params?.steps,
      guidance_scale: hunyuanStats.params?.guidance_scale,
      seed: hunyuanStats.params?.seed,
      octree_resolution: hunyuanStats.params?.octree_resolution,
      num_chunks: hunyuanStats.params?.num_chunks,
      remove_background: hunyuanStats.params?.check_box_rembg,
      texture_enabled: settings?.texture || false
    },
    fileInfo: {
      type: 'GLB',
      size: glbFile?.size || 0,
      vertices: hunyuanStats.number_of_vertices,
      faces: hunyuanStats.number_of_faces
    },
    timing: {}
  };

  // Convert timing information
  if (hunyuanStats.time) {
    modelViewerStats.timing = {
      totalTime: hunyuanStats.time.total,
      modelGenerationTime: hunyuanStats.time['shape generation'],
      textureGenerationTime: hunyuanStats.time['texture generation'],
      postprocessingTime: hunyuanStats.time['postprocessing'],
      faceReductionTime: hunyuanStats.time['face reduction'],
      backgroundRemovalTime: hunyuanStats.time['remove background'],
      exportTime: hunyuanStats.time['export to trimesh']
    };
  }

  return modelViewerStats;
}

// Helper function to determine if progress message should be logged
function shouldLogProgressMessage(message) {
  // Always log completion messages
  if (message.includes('100%|##########|')) {
    return true;
  }

  // For Diffusion Sampling - log every 10% or significant milestones
  if (message.includes('Diffusion Sampling::')) {
    const percentMatch = message.match(/(\d+)%/);
    if (percentMatch) {
      const percent = parseInt(percentMatch[1]);
      // Log every 10% or first/last steps
      if (percent === 0 || percent >= 100 || percent % 10 === 0) {
        if (percent !== lastLoggedDiffusionPercent) {
          lastLoggedDiffusionPercent = percent;
          return true;
        }
      }
    }
    return false;
  }

  // For Volume Decoding - log every 5% to reduce spam
  if (message.includes('Volume Decoding:')) {
    const percentMatch = message.match(/(\d+)%/);
    if (percentMatch) {
      const percent = parseInt(percentMatch[1]);
      // Log every 5% or significant milestones
      if (percent === 0 || percent >= 100 || percent % 5 === 0) {
        if (percent !== lastLoggedVolumePercent) {
          lastLoggedVolumePercent = percent;
          return true;
        }
      }
    }
    return false;
  }

  // For other progress bars - log every 10%
  if (message.includes('%|') && message.includes('it/s]')) {
    const percentMatch = message.match(/(\d+)%/);
    if (percentMatch) {
      const percent = parseInt(percentMatch[1]);
      return percent === 0 || percent >= 100 || percent % 10 === 0;
    }
  }

  // Log non-progress messages
  return true;
}
const CONNECTION_ATTEMPTS_BEFORE_RECOVERY = 999999; // Effectively disabled

// Global progress callback for server output parsing
let globalProgressCallback = null;

// Stage tracking for progress reporting
let currentStage = 'preprocessing';
let stageCompleted = {
  preprocessing: false,
  structure_generation: false,
  glb_generation: false,
  completion: false
};
let progressLineCounter = 0;

// Automated Python process cleanup function
async function cleanupPythonProcesses() {
  logHunyaunServer('Checking for lingering Python processes...');

  const { exec } = require('child_process');
  const util = require('util');
  const execAsync = util.promisify(exec);

  try {
    // More aggressive cleanup - kill all Python processes in the pipeline directory
    const pipelineDir = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/v13_hunyuan2-stableprojectorz');
    const venvPath = path.join(pipelineDir, 'code', 'venv', 'Scripts', 'python.exe');

    // Kill processes by command line pattern
    await execAsync(`taskkill /F /FI "IMAGENAME eq python.exe" /FI "COMMANDLINE eq *${pipelineDir.replace(/\\/g, '\\\\')}*" 2>nul || echo "No matching processes found"`);
    await execAsync(`taskkill /F /FI "IMAGENAME eq python.exe" /FI "COMMANDLINE eq *gradio*" 2>nul || echo "No Gradio processes found"`);
    await execAsync(`taskkill /F /FI "IMAGENAME eq python.exe" /FI "COMMANDLINE eq *uvicorn*" 2>nul || echo "No Uvicorn processes found"`);

    // Also kill any processes using the virtual environment specifically
    await execAsync(`taskkill /F /FI "IMAGENAME eq python.exe" /FI "COMMANDLINE eq *${venvPath.replace(/\\/g, '\\\\')}*" 2>nul || echo "No venv processes found"`);

    // Kill any remaining Hunyuan3D processes
    await execAsync(`taskkill /F /FI "WINDOWTITLE eq *Hunyuan*" 2>nul || echo "No Hunyuan windows found"`);

    // General Python cleanup as fallback
    await execAsync(`taskkill /F /IM python.exe 2>nul || echo "No general Python processes found"`);

    // Extended delay to ensure processes are fully terminated and file handles released
    await new Promise(resolve => setTimeout(resolve, 5000));

    logHunyaunServer('Python processes cleaned up successfully');
  } catch (error) {
    logHunyaunServer('No Python processes found to clean up (or cleanup failed)');
  }
}

const logHunyaunServer = (...args) => {
  const prefix = chalk.red('[HunyaunServer]');
  // Only use console.log to avoid duplication - file logging handled separately
  console.log(prefix, ...args);
};

// Check if Hunyaun server is running and responding to HTTP requests
async function isHunyaunRunning() {
  // Test the actual HTTP endpoint instead of just checking if port is open
  // This prevents issues where the Python process is running but HTTP server is unresponsive

  try {
    // Try the root endpoint for Gradio server health check
    const response = await axios.get(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/`, {
      timeout: 5000 // 5 second timeout for health check
    });

    // Only log successful connection if we had previous failures
    if (connectionAttemptCounter > 0) {
      logHunyaunServer('Server is running - HTTP endpoint responding');
    }
    connectionAttemptCounter = 0; // Reset counter on successful connection
    return true;

  } catch (error) {
    connectionAttemptCounter++;

    // Don't log routine connection errors - they're expected when server is starting
    // Only log significant events like auto-recovery

    // Auto-recovery: If we've failed many times, try to restart the server (Hunyuan takes longer to start)
    if (connectionAttemptCounter >= CONNECTION_ATTEMPTS_BEFORE_RECOVERY && recoveryAttemptCounter < MAX_RECOVERY_ATTEMPTS) {
      logHunyaunServer('🔄 Auto-recovery triggered after ' + CONNECTION_ATTEMPTS_BEFORE_RECOVERY + ' failed attempts (attempt ' + (recoveryAttemptCounter + 1) + '/' + MAX_RECOVERY_ATTEMPTS + ')');
      triggerServerRestart();
    } else if (connectionAttemptCounter >= CONNECTION_ATTEMPTS_BEFORE_RECOVERY && recoveryAttemptCounter >= MAX_RECOVERY_ATTEMPTS) {
      logHunyaunServer('❌ Max recovery attempts (' + MAX_RECOVERY_ATTEMPTS + ') reached. Server may be permanently stuck.');
    }

    return false;
  }
}

async function killExistingHunyuanProcesses() {
  return new Promise((resolve) => {
    logHunyaunServer('Killing any existing Hunyuan3D processes...');

    // Kill any existing python processes running gradio_app.py or Hunyuan3D
    const cleanup = spawn('taskkill', ['/F', '/IM', 'python.exe', '/FI', 'WINDOWTITLE eq *gradio*'], {
      shell: true,
      stdio: 'ignore'
    });

    cleanup.on('close', () => {
      // Also try to kill any processes on port 8080
      const portCleanup = spawn('netstat', ['-ano'], {
        shell: true,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      portCleanup.stdout.on('data', (data) => {
        output += data.toString();
      });

      portCleanup.on('close', () => {
        const lines = output.split('\n');
        const port8080Lines = lines.filter(line => line.includes(':8080'));

        port8080Lines.forEach(line => {
          const parts = line.trim().split(/\s+/);
          const pid = parts[parts.length - 1];
          if (pid && !isNaN(pid)) {
            spawn('taskkill', ['/F', '/PID', pid], { shell: true, stdio: 'ignore' });
          }
        });

        logHunyaunServer('Existing processes cleanup completed');
        resolve();
      });

      portCleanup.on('error', () => resolve()); // Continue even if netstat fails
    });

    cleanup.on('error', () => resolve()); // Continue even if taskkill fails
  });
}

function startHunyaunServer(progressCb) {
  return new Promise(async (resolve, reject) => {
    // Kill any existing processes first to ensure clean start
    await killExistingHunyuanProcesses();

    logHunyaunServer('Starting server...');
    logHunyaunServer('RUN_BAT:', RUN_BAT);
    
    if (!fs.existsSync(RUN_BAT)) {
      const error = `Batch file not found: ${RUN_BAT}`;
      logHunyaunServer(error);
      reject(new Error(error));
      return;
    }
    
    logHunyaunServer('Batch file exists:', fs.existsSync(RUN_BAT));

    // Start the Gradio server as a persistent process - use shell for proper path handling
    global.hunyaunProcess = spawn(`"${RUN_BAT}"`, [], {
      cwd: path.dirname(RUN_BAT),
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true,
      windowsHide: false, // Show window so user can see server status
      detached: false
    });

    let serverStartupTimeout;
    let hasResolved = false;

    // Capture and log relevant output (filter out repetitive status checks)
    global.hunyaunProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        // Filter out repetitive status check messages
        const isStatusCheck = output.includes('GET /status') && output.includes('200 OK');
        const isHealthCheck = output.includes('GET /health') && output.includes('200 OK');

        // Filter out repetitive progress messages - only log significant milestones
        const isProgressMessage = output.includes('%|') || output.includes('it/s]');
        const shouldLogProgress = !isProgressMessage || shouldLogProgressMessage(output);

        // Only log important messages, not repetitive status checks or excessive progress
        if (!isStatusCheck && !isHealthCheck && shouldLogProgress) {
          // Use realtimeLogger only to avoid duplicate logging
          realtimeLogger.logProcessOutput('hunyuan3d', output, 'stdout');
        }

        // Check for specific error patterns
        if (output.includes('Error:') || output.includes('Failed to') || output.includes('not found')) {
          logHunyaunServer('🚨 Error detected in server output:', output);
        }
        
        // Check for successful startup indicators (Gradio server)
        if (output.includes('Application startup complete') ||
            output.includes('Uvicorn running on') ||
            output.includes('Running on local URL') ||
            output.includes('Server will be available at') ||
            output.includes('After launched, open a browser')) {
          if (!hasResolved) {
            hasResolved = true;
            clearTimeout(serverStartupTimeout);
            resolve();
          }
        }
      }
    });

    global.hunyaunProcess.stderr.on('data', (data) => {
      const error = data.toString().trim();
      if (error) {
        // Filter out repetitive status check messages from stderr too
        const isStatusCheck = error.includes('GET /status') && error.includes('200 OK');
        const isHealthCheck = error.includes('GET /health') && error.includes('200 OK');

        // Filter out repetitive progress messages - only log significant milestones
        const isProgressMessage = error.includes('%|') || error.includes('it/s]');
        const shouldLogProgress = !isProgressMessage || shouldLogProgressMessage(error);

        // Only log important messages, not repetitive status checks or excessive progress
        if (!isStatusCheck && !isHealthCheck && shouldLogProgress) {
          // Use realtimeLogger only to avoid duplicate logging
          realtimeLogger.logProcessOutput('hunyuan3d', error, 'stderr');
        }

        // Check for successful startup indicators in STDERR as well
        if (!hasResolved && (error.includes('Application startup complete') ||
            error.includes('Uvicorn running on') ||
            error.includes('Running on local URL') ||
            error.includes('Server will be available at') ||
            error.includes('After launched, open a browser'))) {
          hasResolved = true;
          clearTimeout(serverStartupTimeout);
          logHunyaunServer('✅ Server startup detected in STDERR');
          resolve();
        }

        // Monitor generation progress stages with enhanced tracking
        if (error.includes('Diffusion Sampling:')) {
          const match = error.match(/Diffusion Sampling::\s*(\d+)%/);
          if (match) {
            const progress = parseInt(match[1]);
            // Map to dedicated diffusion stage
            progressCb && progressCb({
              stage: 'hunyuan_diffusion',
              progress,
              step: Math.floor(progress / 5),
              total: 20,
              message: `Diffusion Sampling: ${progress}%`
            });
          }
        } else if (error.includes('Volume Decoding:')) {
          const match = error.match(/Volume Decoding:\s*(\d+)%/);
          if (match) {
            const progress = parseInt(match[1]);
            // Map to dedicated decoding stage
            progressCb && progressCb({
              stage: 'hunyuan_decoding',
              progress,
              step: Math.floor(progress / 5),
              total: 20,
              message: `Volume Decoding: ${progress}%`
            });
          }
        } else if (error.includes('Shape generation takes')) {
          // Texturing stage: Shape generation = 25%
          progressCb && progressCb({
            stage: 'hunyuan_texture',
            progress: 25,
            step: 1,
            total: 4,
            message: 'Shape generation completed'
          });
        } else if (error.includes('Postprocessing takes')) {
          // Texturing stage: Postprocessing = 50%
          progressCb && progressCb({
            stage: 'hunyuan_texture',
            progress: 50,
            step: 2,
            total: 4,
            message: 'Post-processing completed (floater removal)'
          });
        } else if (error.includes('Face Reduction takes')) {
          // Texturing stage: Face Reduction = 75%
          progressCb && progressCb({
            stage: 'hunyuan_texture',
            progress: 75,
            step: 3,
            total: 4,
            message: 'Face reduction completed'
          });
        } else if (error.includes('Texture Generation takes')) {
          // Texturing stage: Texture Generation = 100%
          progressCb && progressCb({
            stage: 'hunyuan_texture',
            progress: 100,
            step: 4,
            total: 4,
            message: 'Texture generation completed'
          });
        } else if (error.includes('Loading') && error.includes('pipeline')) {
          // Handle general loading messages
          progressCb && progressCb({
            stage: 'hunyuan_loading',
            progress: 50,
            step: 5,
            total: 10,
            message: 'Loading pipeline components...'
          });
        } else if (error.includes('Initializing') || error.includes('Starting')) {
          // Handle initialization messages
          progressCb && progressCb({
            stage: 'hunyuan_loading',
            progress: 30,
            step: 3,
            total: 10,
            message: 'Initializing models...'
          });
        }

        // Log specific error types for debugging
        if (error.includes('CUDA') || error.includes('GPU')) {
          logHunyaunServer('🚨 CUDA/GPU Error detected:', error);
        } else if (error.includes('ModuleNotFoundError') || error.includes('ImportError')) {
          logHunyaunServer('🚨 Python Import Error detected:', error);
        } else if (error.includes('Permission denied') || error.includes('Access is denied')) {
          logHunyaunServer('🚨 Permission Error detected:', error);
        }
      }
    });

    global.hunyaunProcess.on('error', (error) => {
      logHunyaunServer('Process error:', error);
      if (!hasResolved) {
        hasResolved = true;
        clearTimeout(serverStartupTimeout);
        reject(error);
      }
    });

    global.hunyaunProcess.on('close', (code, signal) => {
      logHunyaunServer(`Hunyaun server process exited with code: ${code}, signal: ${signal}`);
      
      if (code !== 0 && !hasResolved) {
        hasResolved = true;
        clearTimeout(serverStartupTimeout);
        
        // Provide specific error messages based on exit code
        let errorMessage = `Server failed to start (exit code: ${code})`;
        if (code === 1) {
          errorMessage += ' - This usually indicates a Python error, dependency issue, or CUDA problem';
        } else if (code === 3221225781) {
          errorMessage += ' - This indicates a memory access violation or CUDA driver issue';
        }
        
        reject(new Error(errorMessage));
      }
    });

    // No startup timeout - let server take as long as needed

    logHunyaunServer('Hunyaun server process started');
  });
}

// Wait for Hunyaun server to be ready
async function waitForHunyaunReady(timeout = null, progressCb = null) {
  logHunyaunServer('Waiting for Hunyaun server to be ready...');

  let waitCounter = 0;

  while (true) {
    if (await isHunyaunRunning()) return true;

    waitCounter++;

    // Filter out repetitive "waiting for server" messages - they spam the logs
    // The server startup progress will be shown through other meaningful messages

    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever, no auto-recovery
  }
}

// Auto-recovery function to restart the server after failed attempts
async function triggerServerRestart() {
  try {
    recoveryAttemptCounter++;
    logHunyaunServer('Starting auto-recovery process (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + ')...');

    // Step 1: Kill existing Python processes
    logHunyaunServer('Cleaning up Python processes...');
    await cleanupPythonProcesses();

    // Step 2: Kill existing Hunyaun process if it exists
    if (global.hunyaunProcess) {
      logHunyaunServer('Terminating existing Hunyaun process...');
      global.hunyaunProcess.kill('SIGTERM');
      global.hunyaunProcess = null;
    }

    // Step 3: Wait a moment for cleanup
    logHunyaunServer('Waiting for cleanup to complete...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Step 4: Reset connection counter and restart server
    connectionAttemptCounter = 0;
    logHunyaunServer('Restarting Hunyaun server...');
    startHunyaunServer();

    logHunyaunServer('Auto-recovery process completed (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + ')');

  } catch (error) {
    logHunyaunServer('Auto-recovery failed (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + '):', error);
  }
}

// Reset stage tracking for new generation
function resetStageTracking() {
  currentStage = 'preprocessing';
  stageCompleted = {
    preprocessing: false,
    structure_generation: false,
    glb_generation: false,
    completion: false
  };
  progressLineCounter = 0; // Reset progress line counter
  recoveryAttemptCounter = 0; // Reset recovery counter on successful new generation
  // Reset progress percentage tracking for cleaner logs
  lastLoggedDiffusionPercent = -1;
  lastLoggedVolumePercent = -1;
  logHunyaunServer('[Hunyaun Progress] Stage tracking reset for new generation');
}

function parseProgressFromOutput(output, progressCb) {
  if (!progressCb) {
    return;
  }

  const lines = output.split('\n');

  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine) continue;

    // Check for PROGRESS: JSON format from CLI wrapper
    if (trimmedLine.startsWith('PROGRESS:')) {
      try {
        const progressJson = trimmedLine.substring(9); // Remove "PROGRESS:" prefix
        const progressData = JSON.parse(progressJson);

        // Forward the detailed progress data with proper overall progress
        progressCb({
          stage: progressData.stage,
          progress: progressData.stage_progress || progressData.progress,
          step: progressData.step,
          total: progressData.total,
          stage_progress: progressData.stage_progress || progressData.progress,
          overall_progress: progressData.overall_progress,
          message: progressData.message,
          timestamp: progressData.timestamp
        });
        continue;
      } catch (error) {
        logHunyaunServer('Error parsing progress JSON:', error);
      }
    }

    progressLineCounter++;

    // Only process every 10th line to reduce spam for non-JSON progress
    if (progressLineCounter % 10 !== 0) {
      continue;
    }

    // Map Hunyaun output to progress stages (fallback for non-JSON progress)
    const { stage, description } = mapHunyaunMessageToStage(trimmedLine, 0);

    // Determine progress based on stage
    let progress = 0;
    if (stageCompleted.preprocessing) progress = 25;
    if (stageCompleted.structure_generation) progress = 50;
    if (stageCompleted.glb_generation) progress = 75;
    if (stageCompleted.completion) progress = 100;

    progressCb({ stage, progress, message: description });
  }
}

function mapHunyaunMessageToStage(message, progress) {
  const lowerMessage = message.toLowerCase();

  // Stage 1: Preprocessing
  if (lowerMessage.includes('loading') || lowerMessage.includes('initializing') ||
      lowerMessage.includes('preprocessing') || lowerMessage.includes('image processing')) {
    if (!stageCompleted.preprocessing) {
      currentStage = 'preprocessing';
      if (lowerMessage.includes('complete') || lowerMessage.includes('done')) {
        stageCompleted.preprocessing = true;
      }
    }
    return { stage: 'hunyuan_preprocessing', description: 'Image Preprocessing: Preparing image for 3D generation' };
  }

  // Stage 2: 3D Structure Generation
  if (lowerMessage.includes('generating 3d') || lowerMessage.includes('structure') ||
      lowerMessage.includes('inference') || lowerMessage.includes('diffusion')) {
    if (!stageCompleted.structure_generation) {
      currentStage = 'structure_generation';
      if (lowerMessage.includes('complete') || lowerMessage.includes('done') ||
          lowerMessage.includes('generated')) {
        stageCompleted.structure_generation = true;
      }
    }
    return { stage: 'hunyuan_generation', description: 'Hunyuan3D Generation: Creating 3D structure from image' };
  }

  // Stage 3: GLB Generation
  if (lowerMessage.includes('glb') || lowerMessage.includes('mesh') ||
      lowerMessage.includes('export') || lowerMessage.includes('texture')) {
    if (!stageCompleted.glb_generation) {
      currentStage = 'glb_generation';
      if (lowerMessage.includes('complete') || lowerMessage.includes('done') ||
          lowerMessage.includes('exported')) {
        stageCompleted.glb_generation = true;
      }
    }
    return { stage: 'hunyuan_export', description: 'GLB Export: Finalizing 3D model file' };
  }

  // Stage 4: Post-processing
  if (lowerMessage.includes('postprocessing') || lowerMessage.includes('floater removal') ||
      lowerMessage.includes('degenerate face')) {
    return { stage: 'hunyuan_postprocessing', description: 'Post-processing: Removing floaters and artifacts' };
  }

  // Stage 5: Completion
  if (lowerMessage.includes('complete') || lowerMessage.includes('finished') ||
      lowerMessage.includes('success')) {
    stageCompleted.completion = true;
    return { stage: 'hunyaun_complete', description: 'Generation Complete: 3D model ready' };
  }

  // Default stage based on current stage
  const stageMap = {
    'preprocessing': { stage: 'hunyaun_preprocessing', description: 'Image Preprocessing: Preparing image for 3D generation' },
    'structure_generation': { stage: 'hunyaun_generation', description: 'Hunyaun3D Generation: Creating 3D structure from image' },
    'glb_generation': { stage: 'hunyaun_export', description: 'GLB Export: Finalizing 3D model file' },
    'completion': { stage: 'hunyaun_complete', description: 'Generation Complete: 3D model ready' }
  };

  return stageMap[currentStage] || { stage: 'hunyaun', description: 'Processing...' };
}

// Retry generation request after server restart
async function retryGenerationRequest(imagePath, progressCb, settings = {}) {
  logHunyaunServer('Executing retry generation request...');

  if (progressCb) {
    const { stage, description } = mapHunyaunMessageToStage('Retrying 3D generation...', 0);
    progressCb({ stage, progress: 0, message: description });
  }

  // Upload image to Gradio server first (same as main generation)
  logHunyaunServer('Uploading image to Gradio server for retry...');
  const formData = new FormData();
  formData.append('files', fs.createReadStream(imagePath));

  const uploadResponse = await axios.post(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/upload`, formData, {
    headers: formData.getHeaders(),
    timeout: 30000
  });

  const uploadedImagePath = uploadResponse.data[0];
  logHunyaunServer('Image uploaded successfully for retry:', uploadedImagePath);

  // Use the same request format as main generation
  const requestData = {
    data: [
      null, // caption (None for image-to-3D)
      { path: uploadedImagePath }, // image
      null, // mv_image_front (None for single view)
      null, // mv_image_back (None for single view)
      null, // mv_image_left (None for single view)
      null, // mv_image_right (None for single view)
      settings.num_inference_steps || 25, // steps
      settings.guidance_scale || 5.5, // guidance_scale
      Math.floor(Math.random() * 1000000), // seed
      settings.octree_resolution || 256, // octree_resolution
      true, // check_box_rembg (remove_background)
      settings.num_chunks || 30, // num_chunks
      true // randomize_seed
    ],
    fn_index: settings.texture ? 8 : 5 // Same function index as main generation
  };

  logHunyaunServer('Final retry request data:', {
    fn_index: requestData.fn_index,
    data_length: requestData.data.length,
    image_path: requestData.data[1]?.path || '[NO_IMAGE]'
  });

  // Send generation request to Hunyuan3D-StableProjectorz API
  logHunyaunServer('Sending retry generation request to Hunyuan3D-StableProjectorz API...');
  const requestConfig = {
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 0, // No timeout
    maxRedirects: 5,
    maxContentLength: Infinity,
    maxBodyLength: Infinity,
    keepAlive: true,
    keepAliveMsecs: 30000 // 30 seconds keep alive
  };

  try {
    const response = await axios.post(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/api/predict`, requestData, requestConfig);
    logHunyaunServer('Retry generation request completed successfully');

    if (response.data && response.data.status === 'COMPLETE') {
      // Download the generated model
      const modelResponse = await axios.get(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/download/model`, {
        responseType: 'arraybuffer'
      });

      // Save the model to output directory
      if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR, { recursive: true });
      }

      const outputPath = path.join(OUTPUT_DIR, `hunyuan_retry_${Date.now()}.glb`);
      fs.writeFileSync(outputPath, modelResponse.data);

      logHunyaunServer('Retry model saved to:', outputPath);
      return outputPath;
    } else {
      throw new Error(`Retry generation failed: ${response.data?.message || 'Unknown error'}`);
    }
  } catch (postError) {
    logHunyaunServer('Retry generation request failed:', postError.code, postError.message);
    throw postError;
  }
}

// Check and auto-download Hunyuan models if missing
async function ensureHunyuanModels(progressCb) {
  const path = require('path');
  const fs = require('fs');

  const WORKSPACE_ROOT = path.resolve(__dirname, '../..');
  const MODELS_DIR = path.join(WORKSPACE_ROOT, 'models', 'Hunyuan3D');

  // Check if models exist
  const shapeModelPath = path.join(MODELS_DIR, 'tencent--Hunyuan3D-2.1', 'hunyuan3d-dit-v2-1');
  const textureModelPath = path.join(MODELS_DIR, 'tencent--Hunyuan3D-2.1', 'hunyuan3d-paintpbr-v2-1');

  const shapeModelExists = fs.existsSync(shapeModelPath) && fs.readdirSync(shapeModelPath).length > 0;
  const textureModelExists = fs.existsSync(textureModelPath) && fs.readdirSync(textureModelPath).length > 0;

  if (!shapeModelExists || !textureModelExists) {
    logHunyaunServer('Hunyuan models missing, auto-downloading...');
    progressCb({ stage: 'model_download', progress: 5, message: 'Downloading Hunyuan3D models...' });

    try {
      // Get the dependency manager instance from the main process
      const { dependencyManager } = require('./index');

      // Install models using dependency manager
      await dependencyManager.installDependencies('v13_hunyuan2-stableprojectorz', 'models', 'all');
      logHunyaunServer('Hunyuan models downloaded successfully');
    } catch (error) {
      logHunyaunServer('Failed to download Hunyuan models:', error);
      throw new Error(`Failed to download required models: ${error.message}`);
    }
  }
}

// Main 3D generation function with enhanced diagnostics
async function generate3DModel(imagePath, progressCb, settings = {}) {
  logHunyaunServer('generate3DModel called with imagePath:', imagePath);
  logHunyaunServer('Settings received:', settings);

  // Emit initial loading stage progress
  if (progressCb) {
    progressCb({
      stage: 'hunyuan_loading',
      progress: 0,
      step: 1,
      total: 10,
      message: 'Initializing Hunyuan3D pipeline...'
    });
  }

  // Skip model download - the Gradio server handles this
  // await ensureHunyuanModels(progressCb);

  // First, cleanup any lingering Python processes to prevent permission errors
  await cleanupPythonProcesses();

  if (progressCb) {
    progressCb({
      stage: 'hunyuan_loading',
      progress: 20,
      step: 2,
      total: 10,
      message: 'Checking prerequisites...'
    });
  }

  // Check prerequisites before attempting to start server
  const prerequisitesOk = await checkServerPrerequisites();
  if (!prerequisitesOk) {
    throw new Error('Server prerequisites check failed - please run dependency manager to install Hunyuan3D-2.1');
  }

  if (progressCb) {
    progressCb({
      stage: 'hunyuan_loading',
      progress: 40,
      step: 4,
      total: 10,
      message: 'Checking server status...'
    });
  }

  const isRunning = await isHunyaunRunning();
  logHunyaunServer('isHunyaunRunning() returned:', isRunning);

  // Reset stage tracking for new generation
  resetStageTracking();

  // Set the global progress callback for server output parsing
  globalProgressCallback = progressCb;

  if (!isRunning) {
    logHunyaunServer('Server not running, starting server...');
    if (progressCb) {
      progressCb({
        stage: 'hunyuan_loading',
        progress: 50,
        step: 5,
        total: 10,
        message: 'Starting Hunyuan server...'
      });
    }
    try {
      await startHunyaunServer(progressCb); // Make this await the promise
      logHunyaunServer('startHunyaunServer() completed successfully');
    } catch (error) {
      logHunyaunServer('Error starting server:', error);
      throw error;
    }
    await waitForHunyaunReady(null, progressCb);
  } else {
    logHunyaunServer('Server already running, skipping startup');
    if (progressCb) {
      progressCb({
        stage: 'hunyuan_loading',
        progress: 80,
        step: 8,
        total: 10,
        message: 'Server ready, preparing for generation...'
      });
    }
  }

  if (progressCb) {
    progressCb({
      stage: 'hunyuan_loading',
      progress: 90,
      step: 9,
      total: 10,
      message: 'Loading image and preparing for generation...'
    });
  }

  // Read and encode the image
  const imageBuffer = fs.readFileSync(imagePath);
  const imageBase64 = imageBuffer.toString('base64');
  const imageDataUrl = `data:image/png;base64,${imageBase64}`;

  if (progressCb) {
    progressCb({
      stage: 'hunyuan_loading',
      progress: 100,
      step: 10,
      total: 10,
      message: 'Pipeline loaded, starting generation...'
    });
  }

  // Map UI settings to official Hunyuan3D-2.1 API parameters
  // Based on official API documentation and default values
  let apiSettings = {
    // Official Hunyuan3D-2.1 defaults (from gradio_app.py source)
    guidance_scale: 5.0,        // Official default: cfg_scale = gr.Number(value=5.0)
    num_inference_steps: 30,    // Official default: value=30 (standard mode)
    octree_resolution: 256,     // Official default: value=256

    // Background and seed settings
    remove_background: true,    // Official default: value=True
    randomize_seed: true,       // Official default: value=True
    seed: 1234,                 // Official default: value=1234

    // Processing settings with enhanced post-processing
    num_chunks: 8000,           // Official default: value=8000
    mesh_simplify_ratio: 0.1,   // Enhanced default for better quality

    // Texture generation (enabled by default)
    texture: true,              // Enable PBR texture generation
    texture_size: 2048,         // Enhanced default texture resolution

    // Advanced post-processing settings (enabled by default for better quality)
    remove_floaters: true,
    smooth_normals: true,
    denoise_strength: 1.2,
    multi_view_sampling: true,
    flux_guidance: true,
    jit_acceleration: true,
    delight_toggle: true,

    // Apply user overrides while maintaining enhanced defaults
    ...settings
  };

  logHunyaunServer('Using official Hunyuan3D-2.1 settings with user overrides');

  // Apply safety limits to prevent VRAM issues
  if (apiSettings.guidance_scale !== undefined) {
    apiSettings.guidance_scale = Math.max(0.1, Math.min(20.0, apiSettings.guidance_scale));
  }
  if (apiSettings.num_inference_steps !== undefined) {
    apiSettings.num_inference_steps = Math.max(1, Math.min(100, apiSettings.num_inference_steps));
  }
  if (apiSettings.octree_resolution !== undefined) {
    apiSettings.octree_resolution = Math.max(64, Math.min(512, apiSettings.octree_resolution));
  }

  logHunyaunServer('Mapped API settings:', {
    guidance_scale: apiSettings.guidance_scale,
    num_inference_steps: apiSettings.num_inference_steps,
    octree_resolution: apiSettings.octree_resolution,
    texture: apiSettings.texture,
    remove_background: apiSettings.remove_background,
    num_chunks: apiSettings.num_chunks,
    seed: apiSettings.seed
  });

  // Prepare the request data for Hunyuan3D-StableProjectorz API
  const requestData = {
    single_multi_img_input: [imageDataUrl], // Array of base64 images with data URL prefix
    multiview_mode: false, // Single image mode
    seed: apiSettings.seed,
    guidance_scale: apiSettings.guidance_scale,
    num_inference_steps: apiSettings.num_inference_steps,
    octree_resolution: apiSettings.octree_resolution,
    num_chunks: apiSettings.num_chunks,
    mesh_simplify: 10, // Default mesh simplification
    apply_texture: apiSettings.texture,
    texture_size: 1024
  };

  logHunyaunServer('Final request data:', {
    ...requestData,
    image: '[IMAGE_DATA_OMITTED]' // Don't log the huge base64 image
  });

  try {
    // Send generation request to Hunyuan3D-StableProjectorz API (synchronous)
    logHunyaunServer('Sending generation request to Hunyuan3D-StableProjectorz API...');
    const requestConfig = {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 0, // No timeout for generation
      maxRedirects: 5,
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      keepAlive: true,
      keepAliveMsecs: 30000, // 30 seconds keep alive
      // Add connection stability options
      family: 4, // Force IPv4
      lookup: false, // Disable DNS lookup
      // Add retry and error handling
      retry: 0, // Disable automatic retries (we handle manually)
      validateStatus: function (status) {
        return status >= 200 && status < 300; // Accept only 2xx status codes
      }
    };

    try {
      // First, upload the image to Gradio server
      logHunyaunServer('Uploading image to Gradio server...');

      // Convert base64 to buffer for upload
      const base64Data = imageBase64.replace(/^data:image\/[^;]+;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');

      // Create form data for file upload
      const FormData = require('form-data');
      const form = new FormData();
      form.append('files', imageBuffer, {
        filename: 'input.png',
        contentType: 'image/png'
      });

      // Upload the image
      const uploadResponse = await axios.post(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/upload`, form, {
        headers: {
          ...form.getHeaders()
        },
        timeout: 30000
      });

      if (!uploadResponse.data || uploadResponse.data.length === 0) {
        throw new Error('Failed to upload image to Gradio server');
      }

      const uploadedFilePath = uploadResponse.data[0];
      logHunyaunServer('Image uploaded successfully:', uploadedFilePath);

      // Create FileData object for the uploaded image
      const imageFileData = {
        path: uploadedFilePath,
        meta: { "_type": "gradio.FileData" },
        orig_name: "input.png",
        url: null
      };

      // Use Gradio API endpoint with correct parameter order
      // Based on API config: caption, image, mv_front, mv_back, mv_left, mv_right, steps, guidance_scale, seed, octree_resolution, check_box_rembg, num_chunks, randomize_seed
      const gradioRequestData = {
        data: [
          null,                                    // caption
          imageFileData,                          // image as FileData
          null,                                   // mv_image_front
          null,                                   // mv_image_back
          null,                                   // mv_image_left
          null,                                   // mv_image_right
          settings.num_inference_steps ?? 30,    // steps (official default: 30)
          settings.guidance_scale ?? 5.0,        // guidance_scale (official default: 5.0)
          settings.seed ?? 1234,                 // seed (official default: 1234)
          settings.octree_resolution ?? 256,     // octree_resolution (official default: 256)
          settings.remove_background ?? true,    // check_box_rembg (official default: true)
          settings.num_chunks ?? 8000,           // num_chunks (official default: 8000)
          settings.randomize_seed ?? true        // randomize_seed (official default: true)
        ],
        fn_index: settings.texture ? 8 : 5 // Try function index 8 for textured generation, 5 for shape only
      };

      logHunyaunServer('Using function index:', gradioRequestData.fn_index, settings.texture ? '(textured generation)' : '(shape only)');

      // No timeout for generation process - let it take as long as needed
      const extendedRequestConfig = {
        ...requestConfig,
        timeout: 0 // No timeout - allow unlimited time for generation
      };

      const response = await axios.post(`http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/api/predict`, gradioRequestData, extendedRequestConfig);
      logHunyaunServer('Generation request completed successfully');

      if (response.data && response.data.data) {
        logHunyaunServer('Generation completed successfully');

        // Debug: Log the actual response structure
        logHunyaunServer('Response data structure:', JSON.stringify(response.data, null, 2));

        // Gradio returns data array with [file_out, file_out2, html_gen_mesh, stats, seed]
        const [file_out, file_out2, html_gen_mesh, generationStats, seed] = response.data.data;

        logHunyaunServer('Extracted statistics:', generationStats);

        logHunyaunServer('Extracted files:', {
          file_out: file_out?.value?.path || file_out?.path || '[NO_PATH]',
          file_out2: file_out2?.value?.path || file_out2?.path || '[NO_PATH]',
          stats_available: !!generationStats
        });

        // Try to find the GLB file in either file_out or file_out2
        // Handle both direct path format and wrapped value format
        let glbFile = null;

        // Check file_out (first element)
        if (file_out) {
          if (file_out.value && file_out.value.path && file_out.value.path.includes('.glb')) {
            glbFile = file_out.value; // Extract from value wrapper
          } else if (file_out.path && file_out.path.includes('.glb')) {
            glbFile = file_out; // Direct format
          }
        }

        // Check file_out2 (second element) - prefer textured version if available
        if (file_out2) {
          if (file_out2.value && file_out2.value.path && file_out2.value.path.includes('.glb')) {
            glbFile = file_out2.value; // Extract textured mesh from value wrapper
            logHunyaunServer('Found textured GLB file (preferred):', glbFile.path);
          } else if (file_out2.path && file_out2.path.includes('.glb')) {
            glbFile = file_out2; // Direct format textured mesh
            logHunyaunServer('Found textured GLB file (preferred):', glbFile.path);
          }
        }

        if (glbFile && glbFile.path) {
          logHunyaunServer('Found GLB file:', glbFile.path);

          // Download the generated model file using Gradio's file serving format
          const fileUrl = `http://${HUNYAUN_HOST}:${HUNYAUN_PORT}/file=${glbFile.path}`;
          logHunyaunServer('Downloading from:', fileUrl);

          const modelResponse = await axios.get(fileUrl, {
            responseType: 'arraybuffer',
            timeout: 30000
          });

          // Save the model to output directory
          if (!fs.existsSync(OUTPUT_DIR)) {
            fs.mkdirSync(OUTPUT_DIR, { recursive: true });
          }

          const outputPath = path.join(OUTPUT_DIR, `hunyuan_${Date.now()}.glb`);
          fs.writeFileSync(outputPath, modelResponse.data);

          logHunyaunServer('Model saved to:', outputPath);

          // Format statistics for ModelViewer
          const formattedStats = formatHunyuanStatsForModelViewer(generationStats, glbFile, settings);
          logHunyaunServer('Formatted statistics for ModelViewer:', formattedStats);

          // Cleanup after successful generation to prevent permission errors on next generation
          setTimeout(() => {
            cleanupPythonProcesses().catch(err => {
              logHunyaunServer('Post-generation cleanup warning:', err.message);
            });
          }, 2000); // Small delay to ensure file operations are complete

          return {
            modelPath: outputPath,
            generationStats: formattedStats
          };
        } else {
          // Log all available data for debugging
          logHunyaunServer('No GLB file found. Available data:', response.data.data);
          throw new Error('No GLB model file returned from generation');
        }
      } else {
        throw new Error(`Generation failed: ${response.data?.message || 'Unknown error'}`);
      }
    } catch (postError) {
      logHunyaunServer('Generation request failed:', postError.code, postError.message);
      throw postError;
    }

  } catch (error) {
    logHunyaunServer('Error during 3D generation:', error);

    // If we get connection errors, the server may have crashed or connection issues
    // Try to restart the server and retry once
    if (error.code === 'ECONNREFUSED' || error.code === 'ECONNRESET' ||
        error.message.includes('ECONNREFUSED') || error.message.includes('socket hang up')) {
      logHunyaunServer('Connection refused - server may have crashed. Attempting restart and retry...');

      try {
        // Force cleanup and restart
        await cleanupPythonProcesses();

        // Kill existing process if it exists
        if (global.hunyaunProcess) {
          logHunyaunServer('Killing existing Hunyaun process...');
          global.hunyaunProcess.kill('SIGTERM');
          global.hunyaunProcess = null;
        }

        // Wait for cleanup
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Restart server
        logHunyaunServer('Restarting server after connection failure...');
        startHunyaunServer(progressCb);
        await waitForHunyaunReady(null, progressCb);

        // Retry the generation request once
        logHunyaunServer('Retrying generation request after server restart...');
        return await retryGenerationRequest(imagePath, progressCb, settings);

      } catch (retryError) {
        logHunyaunServer('Retry after server restart failed:', retryError);
        throw retryError;
      }
    }

    throw error;
  }
}

// Diagnostic function to check server prerequisites
async function checkServerPrerequisites() {
  logHunyaunServer('🔍 Checking server prerequisites...');
  
  const issues = [];
  
  // Check if batch file exists
  if (!fs.existsSync(RUN_BAT)) {
    issues.push(`Batch file not found: ${RUN_BAT}`);
  }
  
  // Check if the Gradio server directory structure exists
  const gradioServerDir = path.dirname(RUN_BAT);
  if (!fs.existsSync(gradioServerDir)) {
    issues.push(`Gradio server directory not found: ${gradioServerDir}`);
  }

  // Check if the main v13_hunyuan2-stableprojectorz directory exists
  const mainServerDir = path.join(gradioServerDir, '..');
  if (!fs.existsSync(mainServerDir)) {
    issues.push(`Main server directory not found: ${mainServerDir}`);
  }

  // For Gradio server, we just need the batch file to exist
  // The batch file will handle environment setup and dependencies
  
  if (issues.length > 0) {
    logHunyaunServer('Prerequisites check failed:');
    issues.forEach(issue => logHunyaunServer('  ', issue));
    return false;
  }
  
  logHunyaunServer('✅ All prerequisites check passed');
  return true;
}

module.exports = {
  generate3DModel,
  cleanupPythonProcesses,
  isHunyaunRunning,
  startHunyaunServer,
  waitForHunyaunReady,
  checkServerPrerequisites
};



