# Core dependencies
ninja==1.11.1.4
pybind11==2.13.6

# Image generation and processing
diffusers==0.32.2
einops==0.8.1
imageio==2.37.0
numpy==1.25.2
omegaconf==2.3.0
opencv-python==4.11.0.86
opencv-python-headless==4.11.0.86
pillow==10.4.0
transformers==4.50.3

# Utilities
tqdm==4.67.1

# Mesh Processing
pygltflib==1.16.3
pymeshlab==2023.12.post3
trimesh==4.6.5
xatlas==0.0.9


# Demo only
fastapi==0.115.6
gradio==4.44.1
gradio_client==1.3.0
gradio_litmodel3d==0.0.1
matplotlib==3.10.1
onnxruntime==1.21.0
rembg==2.0.65
uvicorn==0.34.0
websockets==12.0

# Dependencies and additional packages
aiofiles==23.2.1
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
attrs==25.3.0
certifi==2022.12.7
charset-normalizer==2.1.1
click==8.1.8
colorama==0.4.6
coloredlogs==15.0.1
contourpy==1.3.1
cycler==0.12.1
dataclasses-json==0.6.7
Deprecated==1.2.18
ffmpy==0.5.0
filelock==3.13.1
flatbuffers==25.2.10
fonttools==4.56.0
fsspec==2024.6.1
groovy==0.1.2
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.29.3
humanfriendly==10.0
idna==3.4
importlib_metadata==8.6.1
importlib_resources==6.5.2
Jinja2==3.1.4
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kiwisolver==1.4.8
lazy_loader==0.4
llvmlite==0.44.0
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.26.1
mdurl==0.1.2
mpmath==1.3.0
msvc_runtime==14.42.34433
mypy-extensions==1.0.0
networkx==3.3
numba==0.61.0
orjson==3.10.16
packaging==24.2
pandas==2.2.3
platformdirs==4.3.7
pooch==1.8.2
protobuf==6.30.2
psutil==7.0.0
pydantic==2.10.5
pydantic_core==2.27.2
pydub==0.25.1
Pygments==2.19.1
PyMatting==1.1.13
pyparsing==3.2.3
pyreadline3==3.5.4
python-dateutil==2.9.0.post0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rpds-py==0.24.0
ruff==0.11.2
safehttpx==0.1.6
safetensors==0.5.3
scikit-image==0.25.2
scipy==1.15.2
semantic-version==2.10.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
starlette==0.41.3
sympy==1.13.1
tifffile==2025.3.13
tokenizers==0.21.1
tomlkit==0.12.0
typer==0.15.2
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.12.2
tzdata==2025.2
urllib3==2.3.0
websockets==12.0
wrapt==1.17.2
zipp==3.21.0