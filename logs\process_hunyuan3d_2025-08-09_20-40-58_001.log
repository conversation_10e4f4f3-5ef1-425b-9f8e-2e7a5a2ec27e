========================================
AIStudio Real-time Log: hunyuan3d
Started: 2025-08-10T01:40:58.045Z
File: process_hunyuan3d_2025-08-09_20-40-58_001.log
========================================

[2025-08-10T01:40:58.044Z] [STDOUT] Using AIStudio models directory: M:\AIStudio\models
[2025-08-10T01:40:58.110Z] [STDOUT] F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.
[2025-08-10T01:40:58.111Z] [STDOUT] "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe"
[2025-08-10T01:40:58.111Z] [STDOUT] "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\get-pip.py"
[2025-08-10T01:41:01.210Z] [STDOUT] Creating virtual environment using portable Python...
[2025-08-10T01:41:01.210Z] [STDOUT] "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe" -m virtualenv "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv"
