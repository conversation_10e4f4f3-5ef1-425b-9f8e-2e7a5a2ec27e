#!/usr/bin/env python3
"""
Test script to verify progress tracking functionality for both Hunyuan3D and Trellis pipelines.
This script simulates the progress tracking without actually running the full 3D generation.
"""

import sys
import os
import json
import time
import argparse

def emit_progress(stage, progress, step=None, total=None, message="", timestamp=None):
    """Emit progress update in JSON format for Node.js backend to parse"""
    if timestamp is None:
        timestamp = time.time()
    
    progress_data = {
        "stage": stage,
        "progress": min(100, max(0, progress)),
        "step": step or 1,
        "total": total or 1,
        "stage_progress": min(100, max(0, progress)),
        "overall_progress": min(100, max(0, progress)),
        "message": message,
        "timestamp": timestamp
    }
    
    # Emit as JSON that Node.js can parse
    print(f"PROGRESS:{json.dumps(progress_data)}", flush=True)

def test_hunyuan_progress():
    """Test Hunyuan3D progress tracking with new stages"""
    print("Testing Hunyuan3D Progress Tracking", flush=True)

    # Stage 1: Loading Pipeline
    emit_progress("hunyuan_loading", 0, 1, 10, "Initializing Hunyuan3D pipeline...")
    time.sleep(0.5)
    emit_progress("hunyuan_loading", 20, 2, 10, "Checking prerequisites...")
    time.sleep(0.5)
    emit_progress("hunyuan_loading", 40, 4, 10, "Checking server status...")
    time.sleep(0.5)
    emit_progress("hunyuan_loading", 60, 6, 10, "Loading models...")
    time.sleep(0.5)
    emit_progress("hunyuan_loading", 80, 8, 10, "Server ready, preparing for generation...")
    time.sleep(0.5)
    emit_progress("hunyuan_loading", 100, 10, 10, "Pipeline loaded, starting generation...")
    time.sleep(0.5)

    # Stage 2: Diffusion Sampling
    emit_progress("hunyuan_diffusion", 0, 1, 20, "Starting diffusion sampling...")
    time.sleep(0.5)

    for step in range(1, 21):
        progress = int((step / 20) * 100)
        emit_progress("hunyuan_diffusion", progress, step, 20, f"Diffusion Sampling: {progress}%")
        time.sleep(0.2)

    # Stage 3: Volume Decoding
    emit_progress("hunyuan_decoding", 0, 1, 20, "Starting volume decoding...")
    time.sleep(0.5)

    for step in range(1, 21):
        progress = int((step / 20) * 100)
        emit_progress("hunyuan_decoding", progress, step, 20, f"Volume Decoding: {progress}%")
        time.sleep(0.15)

    # Stage 4: Texturing Mesh
    emit_progress("hunyuan_texture", 25, 1, 4, "Shape generation completed")
    time.sleep(0.8)
    emit_progress("hunyuan_texture", 50, 2, 4, "Post-processing completed (floater removal)")
    time.sleep(0.8)
    emit_progress("hunyuan_texture", 75, 3, 4, "Face reduction completed")
    time.sleep(0.8)
    emit_progress("hunyuan_texture", 100, 4, 4, "Texture generation completed")
    
    result = {
        "success": True,
        "message": "Hunyuan3D test completed successfully",
        "output_path": "/test/output.glb"
    }
    print(f"RESULT:{json.dumps(result)}", flush=True)

def test_trellis_progress():
    """Test Trellis progress tracking"""
    print("Testing Trellis Progress Tracking", flush=True)
    
    # Stage 1: Preprocessing
    emit_progress("preprocessing", 0, 1, 5, "Loading image and initializing pipeline")
    time.sleep(0.5)
    emit_progress("preprocessing", 100, 5, 5, "Image preprocessing complete")
    time.sleep(0.5)
    
    # Stage 2: Sparse Structure
    emit_progress("sparse_structure", 0, 1, 12, "Generating 3D structure foundation...")
    time.sleep(0.5)
    
    for step in range(1, 13):
        progress = int((step / 12) * 100)
        emit_progress("sparse_structure", progress, step, 12, f"Sparse structure step {step}/12")
        time.sleep(0.3)
    
    # Stage 3: SLAT Generation
    emit_progress("slat_generation", 0, 1, 12, "Creating detailed 3D representation...")
    time.sleep(0.5)
    
    for step in range(1, 13):
        progress = int((step / 12) * 100)
        emit_progress("slat_generation", progress, step, 12, f"SLAT generation step {step}/12")
        time.sleep(0.3)
    
    # Stage 4: Mesh Creation
    emit_progress("mesh_creation", 0, 1, 5, "Converting to mesh format...")
    time.sleep(0.5)
    emit_progress("mesh_creation", 50, 3, 5, "Processing mesh geometry...")
    time.sleep(0.5)
    emit_progress("mesh_creation", 100, 5, 5, "Mesh creation complete")
    time.sleep(0.5)
    
    # Stage 5: GLB Export
    emit_progress("glb_export", 0, 1, 3, "Rendering videos...")
    time.sleep(0.5)
    emit_progress("glb_export", 50, 2, 3, "Exporting GLB file...")
    time.sleep(0.5)
    emit_progress("glb_export", 100, 3, 3, "GLB export completed")
    
    result = {
        "success": True,
        "message": "Trellis test completed successfully",
        "output_path": "/test/output.glb"
    }
    print(f"RESULT:{json.dumps(result)}", flush=True)

def main():
    parser = argparse.ArgumentParser(description="Test progress tracking for 3D pipelines")
    parser.add_argument("--pipeline", choices=["hunyuan", "trellis"], default="hunyuan",
                       help="Which pipeline to test")
    
    args = parser.parse_args()
    
    try:
        if args.pipeline == "hunyuan":
            test_hunyuan_progress()
        elif args.pipeline == "trellis":
            test_trellis_progress()
        else:
            print(f"Unknown pipeline: {args.pipeline}", file=sys.stderr)
            sys.exit(1)
            
    except Exception as e:
        error_msg = f"Error in progress tracking test: {str(e)}"
        print(f"ERROR:{error_msg}", file=sys.stderr, flush=True)
        
        result = {
            "success": False,
            "error": error_msg
        }
        print(f"RESULT:{json.dumps(result)}", flush=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
