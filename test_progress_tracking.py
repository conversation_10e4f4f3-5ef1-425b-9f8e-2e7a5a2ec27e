#!/usr/bin/env python3

import asyncio
import websockets
import json
import time

async def test_progress_tracking():
    """Test the progress tracking with proper stage progression"""
    
    # Connect to the WebSocket server
    uri = "ws://localhost:8765"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("Connected to WebSocket server")
            
            # Test sequence: Each stage should complete before moving to next
            test_stages = [
                # Stage 1: Loading Pipeline (10% weight)
                {"stage": "hunyuan_loading", "progress": 25, "message": "Initializing Hunyuan pipeline and models"},
                {"stage": "hunyuan_loading", "progress": 50, "message": "Loading model weights"},
                {"stage": "hunyuan_loading", "progress": 75, "message": "Setting up pipeline"},
                {"stage": "hunyuan_loading", "progress": 100, "message": "Pipeline loaded successfully"},
                
                # Stage 2: Diffusion Sampling (40% weight)  
                {"stage": "hunyuan_diffusion", "progress": 20, "message": "Diffusion Sampling: 20%"},
                {"stage": "hunyuan_diffusion", "progress": 40, "message": "Diffusion Sampling: 40%"},
                {"stage": "hunyuan_diffusion", "progress": 60, "message": "Diffusion Sampling: 60%"},
                {"stage": "hunyuan_diffusion", "progress": 80, "message": "Diffusion Sampling: 80%"},
                {"stage": "hunyuan_diffusion", "progress": 100, "message": "Diffusion sampling complete"},
                
                # Stage 3: Volume Decoding (30% weight)
                {"stage": "hunyuan_decoding", "progress": 25, "message": "Converting diffusion output to 3D volume"},
                {"stage": "hunyuan_decoding", "progress": 50, "message": "Processing 3D volume data"},
                {"stage": "hunyuan_decoding", "progress": 75, "message": "Optimizing volume structure"},
                {"stage": "hunyuan_decoding", "progress": 100, "message": "Volume decoding complete"},
                
                # Stage 4: Texturing Mesh (20% weight)
                {"stage": "hunyuan_texture", "progress": 30, "message": "Processing mesh and applying textures"},
                {"stage": "hunyuan_texture", "progress": 60, "message": "Refining surface details"},
                {"stage": "hunyuan_texture", "progress": 90, "message": "Finalizing textures"},
                {"stage": "hunyuan_texture", "progress": 100, "message": "3D model generation complete!"}
            ]
            
            print("Starting progress test sequence...")
            
            for i, update in enumerate(test_stages):
                # Send progress update
                message = {
                    "type": "progress_update",
                    "data": update
                }
                
                await websocket.send(json.dumps(message))
                print(f"Sent update {i+1}/{len(test_stages)}: {update['stage']} - {update['progress']}%")
                
                # Wait between updates to simulate real processing
                await asyncio.sleep(1.5)
            
            print("Test sequence completed!")
            
            # Keep connection open for a bit to see final state
            await asyncio.sleep(5)
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_progress_tracking())
