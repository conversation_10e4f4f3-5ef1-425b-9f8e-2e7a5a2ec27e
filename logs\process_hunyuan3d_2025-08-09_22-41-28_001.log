========================================
AIStudio Real-time Log: hunyuan3d
Started: 2025-08-10T03:41:28.243Z
File: process_hunyuan3d_2025-08-09_22-41-28_001.log
========================================

[2025-08-10T03:41:28.242Z] [STDOUT] Using AIStudio models directory: M:\AIStudio\models
[2025-08-10T03:41:28.286Z] [STDOUT] F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.
[2025-08-10T03:41:28.287Z] [STDOUT] "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe"
[2025-08-10T03:41:28.288Z] [STDOUT] "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\get-pip.py"
[2025-08-10T03:41:29.140Z] [STDOUT] Creating virtual environment using portable Python...
[2025-08-10T03:41:29.140Z] [STDOUT] "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe" -m virtualenv "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv"
[2025-08-10T03:41:33.838Z] [STDOUT] sitecustomize.py applied
created virtual environment CPython3.11.9.final.0-64 in 4289ms
  creator CPython3Windows(dest=F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv, clear=False, no_vcs_ignore=False, global=False)
  seeder FromAppData(download=False, pip=bundle, setuptools=bundle, via=copy, app_data_dir=C:\Users\<USER>\AppData\Local\pypa\virtualenv)
    added seed packages: Deprecated==1.2.18, MarkupSafe==2.1.5, PyMatting==1.1.13, PyYAML==6.0.2, accelerate==1.5.2, aiofiles==23.2.1, annotated_types==0.7.0, antlr4_python3_runtime==4.9.3, anyio==4.9.0, attrs==25.3.0, certifi==2022.12.7, charset_normalizer==2.1.1, click==8.1.8, colorama==0.4.6, coloredlogs==15.0.1, contourpy==1.3.1, custom_rasterizer==0.1, cycler==0.12.1, dataclasses_json==0.6.7, diffusers==0.32.2, einops==0.8.1, fastapi==0.115.6, ffmpy==0.5.0, filelock==3.13.1, flatbuffers==25.2.10, fonttools==4.56.0, fsspec==2024.6.1, gradio==4.44.1, gradio_client==1.3.0, gradio_litmodel3d==0.0.1, groovy==0.1.2, h11==0.14.0, httpcore==1.0.7, httpx==0.28.1, huggingface_hub==0.29.3, humanfriendly==10.0, idna==3.4, imageio==2.37.0, importlib_metadata==8.6.1, importlib_resources==6.5.2, jinja2==3.1.4, jsonschema==4.23.0, jsonschema_specifications==2024.10.1, kiwisolver==1.4.8, lazy_loader==0.4, llvmlite==0.44.0, markdown_it_py==3.0.0, marshmallow==3.26.1, matplotlib==3.10.1, mdurl==0.1.2, mesh_processor==0.1.0, mpmath==1.3.0, msvc_runtime==14.42.34433, mypy_extensions==1.0.0, networkx==3.3, ninja==********, numba==0.61.0, numpy==1.25.2, omegaconf==2.3.0, onnxruntime==1.21.0, opencv_python==*********, opencv_python_headless==*********, orjson==3.10.16, packaging==24.2, pandas==2.2.3, pillow==10.4.0, pip==25.1.1, platformdirs==4.3.7, pooch==1.8.2, protobuf==6.30.2, psutil==7.0.0, pybind11==2.13.6, pydantic==2.10.5, pydantic_core==2.27.2, pydub==0.25.1, pygltflib==1.16.3, pygments==2.19.1, pymeshlab==2023.12.post3, pyparsing==3.2.3, pyreadline3==3.5.4, python_dateutil==2.9.0.post0, python_multipart==0.0.20, pytz==2025.2, referencing==0.36.2, regex==2024.11.6, rembg==2.0.65, requests==2.32.3, rich==13.9.4, rpds_py==0.24.0, ruff==0.11.2, safehttpx==0.1.6, safetensors==0.5.3, scikit_image==0.25.2, scipy==1.15.2, semantic_version==2.10.0, setuptools==80.9.0, shellingham==1.5.4, six==1.17.0, sniffio==1.3.1, starlette==0.41.3, sympy==1.13.1, tifffile==2025.3.13, tokenizers==0.21.1, tomlkit==0.12.0, torch==2.5.1+cu124, torchaudio==2.5.1+cu124, torchvision==0.20.1+cu124, tqdm==4.67.1, transformers==4.50.3, trimesh==4.6.5, typer==0.15.2, typing_extensions==4.12.2, typing_inspect==0.9.0, typing_inspection==0.4.0, tzdata==2025.2, urllib3==2.3.0, uvicorn==0.34.0, websockets==12.0, wrapt==1.17.2, xatlas==0.0.9, zipp==3.21.0
  activators BashActivator,BatchActivator,FishActivator,NushellActivator,PowerShellActivator,PythonActivator
[2025-08-10T03:41:33.889Z] [STDOUT] 1 file(s) copied.
[2025-08-10T03:41:34.152Z] [STDOUT] Portable Python located at: F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe
[2025-08-10T03:41:34.152Z] [STDOUT] Virtual environment Python set to: "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-10T03:41:34.154Z] [STDOUT] _
[2025-08-10T03:41:34.154Z] [STDOUT] Current Python: "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-10T03:41:34.155Z] [STDOUT] Virtual Env: F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-10T03:41:34.155Z] [STDOUT] _
[2025-08-10T03:41:34.160Z] [STDOUT] Current Python: "F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-10T03:41:34.161Z] [STDOUT] Virtual Env: F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-10T03:41:34.161Z] [STDOUT] Starting the server, please wait...
[2025-08-10T03:41:49.707Z] [STDERR] F:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv\Lib\site-packages\transformers\utils\hub.py:105: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
[2025-08-10T03:41:53.031Z] [STDOUT] Loading example img list ...
Loading example txt list ...
Loading example mv list ...
[2025-08-10T03:41:53.032Z] [STDERR] Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
[2025-08-10T03:41:53.033Z] [STDERR] Fetching 13 files: 100%|##########| 13/13 [00:00<00:00, 12997.84it/s]
[2025-08-10T03:41:53.147Z] [STDERR] Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
[2025-08-10T03:41:53.150Z] [STDERR] Fetching 17 files: 100%|##########| 17/17 [00:00<00:00, 8502.64it/s]
[2025-08-10T03:41:53.240Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-10T03:41:53.420Z] [STDERR] Loading pipeline components...:  50%|#####     | 3/6 [00:00<00:00, 16.77it/s]
[2025-08-10T03:41:55.473Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:02<00:00,  2.39it/s]
[2025-08-10T03:41:55.475Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:02<00:00,  2.69it/s]
[2025-08-10T03:41:57.599Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-10T03:42:13.345Z] [STDERR] Loading pipeline components...:  67%|######6   | 4/6 [00:15<00:07,  3.94s/it]
[2025-08-10T03:42:13.507Z] [STDERR] Loading pipeline components...:  83%|########3 | 5/6 [00:15<00:02,  2.94s/it]
[2025-08-10T03:42:15.432Z] [STDERR] Loading pipeline components...: 100%|##########| 6/6 [00:17<00:00,  2.66s/it]
Loading pipeline components...: 100%|##########| 6/6 [00:17<00:00,  2.97s/it]
[2025-08-10T03:42:27.443Z] [STDERR] 2025-08-09 22:42:27,443 - hy3dgen.shapgen - INFO - Try to load model from local path: M:\AIStudio\models\tencent/Hunyuan3D-2\hunyuan3d-dit-v2-0
[2025-08-10T03:42:27.444Z] [STDERR] 2025-08-09 22:42:27,443 - hy3dgen.shapgen - INFO - Model path not exists, try to download from huggingface
[2025-08-10T03:42:27.547Z] [STDERR] Fetching 6 files:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-10T03:42:27.550Z] [STDERR] Fetching 6 files: 100%|##########| 6/6 [00:00<00:00, 2940.62it/s]
[2025-08-10T03:42:27.565Z] [STDERR] 2025-08-09 22:42:27,565 - hy3dgen.shapgen - INFO - Loading model from M:\AIStudio\models\models--tencent--Hunyuan3D-2\snapshots\ac9fd33683d032aedf4f5e95efd204c5aac700d6\hunyuan3d-dit-v2-0\model.fp16.safetensors
[2025-08-10T03:43:16.369Z] [STDERR] INFO:     Started server process [4580]
INFO:     Waiting for application startup.
[2025-08-10T03:43:16.369Z] [STDERR] INFO:     Application startup complete.
[2025-08-10T03:43:16.382Z] [STDERR] INFO:     Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
[2025-08-10T03:43:16.407Z] [STDOUT] After launched, open a browser and enter 0.0.0.0:8080 into url, as if it was a website:
INFO:     127.0.0.1:55016 - "GET / HTTP/1.1" 200 OK
[2025-08-10T03:43:16.685Z] [STDOUT] INFO:     127.0.0.1:55016 - "POST /upload HTTP/1.1" 200 OK
[2025-08-10T03:43:22.152Z] [STDOUT] Created new folder: gradio_cache\c5674a39-5129-4673-8279-ebb9f934a674
[2025-08-10T03:43:22.152Z] [STDERR] Diffusion Sampling::   0%|          | 0/25 [00:00<?, ?it/s]
[2025-08-10T03:43:27.121Z] [STDERR] Diffusion Sampling::  20%|##        | 5/25 [00:04<00:21,  1.07s/it]
[2025-08-10T03:43:33.173Z] [STDERR] Diffusion Sampling::  40%|####      | 10/25 [00:11<00:17,  1.19s/it]
[2025-08-10T03:43:39.238Z] [STDERR] Diffusion Sampling::  60%|######    | 15/25 [00:17<00:12,  1.21s/it]
[2025-08-10T03:43:45.374Z] [STDERR] Diffusion Sampling::  80%|########  | 20/25 [00:23<00:06,  1.23s/it]
[2025-08-10T03:43:51.559Z] [STDERR] Diffusion Sampling:: 100%|##########| 25/25 [00:29<00:00,  1.23s/it]
Diffusion Sampling:: 100%|##########| 25/25 [00:29<00:00,  1.18s/it]
[2025-08-10T03:43:52.795Z] [STDERR] Volume Decoding:   0%|          | 0/565820 [00:00<?, ?it/s]
[2025-08-10T03:44:33.700Z] [STDERR] Volume Decoding:   5%|4         | 25492/565820 [00:40<14:45, 610.23it/s]
[2025-08-10T03:45:19.592Z] [STDERR] Volume Decoding:  10%|9         | 53802/565820 [01:26<13:39, 624.59it/s]
[2025-08-10T03:46:05.722Z] [STDERR] Volume Decoding:  15%|#4        | 82073/565820 [02:12<14:35, 552.41it/s]
[2025-08-10T03:46:51.268Z] [STDERR] Volume Decoding:  20%|#9        | 110389/565820 [02:58<12:09, 624.63it/s]
[2025-08-10T03:47:37.353Z] [STDERR] Volume Decoding:  25%|##4       | 138628/565820 [03:44<11:26, 621.84it/s]
[2025-08-10T03:48:22.970Z] [STDERR] Volume Decoding:  30%|##9       | 166954/565820 [04:30<10:42, 620.78it/s]
[2025-08-10T03:49:08.533Z] [STDERR] Volume Decoding:  35%|###4      | 195247/565820 [05:15<09:54, 623.00it/s]
[2025-08-10T03:49:53.823Z] [STDERR] Volume Decoding:  40%|###9      | 223510/565820 [06:01<09:03, 629.71it/s]
[2025-08-10T03:50:39.276Z] [STDERR] Volume Decoding:  45%|####4     | 251805/565820 [06:46<08:20, 627.60it/s]
[2025-08-10T03:51:25.751Z] [STDERR] Volume Decoding:  50%|####9     | 280141/565820 [07:32<07:36, 626.23it/s]
[2025-08-10T03:52:11.899Z] [STDERR] Volume Decoding:  55%|#####4    | 308429/565820 [08:19<06:54, 620.84it/s]
[2025-08-10T03:52:58.233Z] [STDERR] Volume Decoding:  60%|#####9    | 336725/565820 [09:05<06:20, 601.91it/s]
[2025-08-10T03:53:44.228Z] [STDERR] Volume Decoding:  65%|######4   | 364982/565820 [09:51<05:24, 619.38it/s]
[2025-08-10T03:54:30.212Z] [STDERR] Volume Decoding:  70%|######9   | 393308/565820 [10:37<04:37, 622.00it/s]
[2025-08-10T03:55:16.112Z] [STDERR] Volume Decoding:  75%|#######4  | 421581/565820 [11:23<04:00, 598.82it/s]
[2025-08-10T03:56:02.158Z] [STDERR] Volume Decoding:  80%|#######9  | 449838/565820 [12:09<03:04, 627.73it/s]
[2025-08-10T03:56:48.215Z] [STDERR] Volume Decoding:  85%|########4 | 478138/565820 [12:55<02:21, 618.58it/s]
[2025-08-10T03:57:34.771Z] [STDERR] Volume Decoding:  90%|########9 | 506413/565820 [13:41<01:35, 622.45it/s]
[2025-08-10T03:58:20.881Z] [STDERR] Volume Decoding:  95%|#########4| 534705/565820 [14:28<00:49, 622.43it/s]
[2025-08-10T03:59:07.043Z] [STDERR] Volume Decoding: 100%|#########9| 562996/565820 [15:14<00:04, 623.14it/s]
[2025-08-10T03:59:11.676Z] [STDERR] Volume Decoding: 100%|##########| 565820/565820 [15:18<00:00, 615.77it/s]
[2025-08-10T03:59:14.352Z] [STDERR] 2025-08-09 22:59:14,352 - hy3dgen.shapgen - INFO - ---Shape generation takes 955.6488208770752 seconds ---
[2025-08-10T03:59:18.185Z] [STDERR] 2025-08-09 22:59:18,184 - hy3dgen.shapgen - INFO - ---Postprocessing takes 3.3340961933135986 seconds ---
[2025-08-10T03:59:35.490Z] [STDERR] 2025-08-09 22:59:35,489 - hy3dgen.shapgen - INFO - ---Face Reduction takes 17.30509614944458 seconds ---
[2025-08-10T04:01:09.817Z] [STDERR] 2025-08-09 23:01:09,817 - hy3dgen.shapgen - INFO - ---Texture Generation takes 94.32708764076233 seconds ---
[2025-08-10T04:01:11.744Z] [STDOUT] Find html file gradio_cache\c5674a39-5129-4673-8279-ebb9f934a674\textured_mesh.html, True, relative HTML path is /static/c5674a39-5129-4673-8279-ebb9f934a674\textured_mesh.html
INFO:     127.0.0.1:55017 - "POST /api/predict HTTP/1.1" 200 OK
[2025-08-10T04:01:11.779Z] [STDOUT] INFO:     127.0.0.1:55150 - "GET /file%3DC%3A/Users/<USER>/AppData/Local/Temp/gradio/a22d14099de4f8dacceb5b6d427fe8685c4097ec7391b769fb00193cf4d03cce/textured_mesh.glb HTTP/1.1" 200 OK
[2025-08-10T04:01:18.532Z] [STDOUT] Something went wrong, consider removing code/hunyuan_init_done.txt and the venv folder to re-initialize from scratch
[2025-08-10T04:01:18.533Z] [STDOUT] Press any key to continue . . .
