import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>cle, Circle, Loader2, <PERSON>, Zap, Cpu, Download, ChevronDown, ChevronUp, Type, Sparkles } from 'lucide-react';

interface ProgressStage {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  status: 'pending' | 'active' | 'completed' | 'error';
  estimatedTime?: string;
}

interface ProgressBarProps {
  isVisible: boolean;
  isDarkMode: boolean;
  sessionId?: string;
  generationMode?: 'text-to-3d' | 'image-to-3d';
  delighterEnabled?: boolean;
  onComplete?: () => void;
  onError?: (error: string) => void;
  pipeline: 'trellis' | 'hunyuan'; // NEW PROP
}

const defaultStages: ProgressStage[] = [
  {
    id: 'text_to_image',
    name: 'Text-to-Image Generation',
    description: 'Generating image from text prompt using AI',
    icon: <Type className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '5s'
  },
  {
    id: 'preprocessing',
    name: 'Image Preprocessing',
    description: 'Loading image and initializing pipeline',
    icon: <Circle className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '5s'
  },
  {
    id: 'sparse_structure',
    name: 'Sparse Structure Sampling',
    description: 'Generating 3D structure foundation (12 steps)',
    icon: <Cpu className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '60s'
  },
  {
    id: 'slat_generation',
    name: 'SLAT Generation Sampling',
    description: 'Creating detailed 3D representation (12 steps)',
    icon: <Zap className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '40s'
  },
  {
    id: 'mesh_creation',
    name: 'Mesh Processing',
    description: 'Decimating mesh and rendering',
    icon: <Cpu className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '60s'
  },
  {
    id: 'texture_generation',
    name: 'Texture Generation',
    description: 'Optimizing texture baking (2500 steps)',
    icon: <Sparkles className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '80s'
  },
  {
    id: 'glb_export',
    name: 'GLB Export',
    description: 'Finalizing and exporting 3D model',
    icon: <Download className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '15s'
  },
  {
    id: 'download',
    name: 'Model Download',
    description: 'Downloading generated 3D model',
    icon: <Download className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '10s'
  },
  {
    id: 'texture_enhancement',
    name: 'Texture Enhancement',
    description: 'Enhancing textures with Agisoft De-Lighter',
    icon: <Sparkles className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '10s'
  },
  {
    id: 'hunyaun',
    name: 'Hunyuan3D-2 Generation',
    description: 'Generating 3D model with Hunyuan3D-2 pipeline',
    icon: <Cpu className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '60s'
  }
];

// Add helpers to get Trellis and Hunyuan stages
const getTrellisStages = (includeDelighter: boolean = false) => {
  // All times cut in half: preprocessing 2s, sparse_structure 30s, slat_generation 20s, mesh_creation 30s, texture_generation 40s, glb_export 8s, download 3s
  const safeStage = (id: string, estimatedTime: string): ProgressStage => {
    const s = defaultStages.find(stage => stage.id === id);
    return {
      id: s?.id || id,
      name: s?.name || id,
      description: s?.description || '',
      icon: s?.icon || <Circle className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime
    };
  };
  let stages: ProgressStage[] = [
    safeStage('preprocessing', '2s'),
    safeStage('sparse_structure', '30s'),
    safeStage('slat_generation', '20s'),
    safeStage('mesh_creation', '30s'),
    safeStage('texture_generation', '40s'),
    safeStage('glb_export', '8s'),
    safeStage('download', '3s')
  ];
  if (includeDelighter) {
    // Insert delighter stage before GLB Export if enabled
    const delighterStage = defaultStages.find(s => s.id === 'texture_enhancement');
    if (delighterStage) {
      const idx = stages.findIndex(s => s.id === 'glb_export');
      if (idx !== -1) {
        stages = [
          ...stages.slice(0, idx),
          {
            id: delighterStage.id,
            name: delighterStage.name,
            description: delighterStage.description,
            icon: delighterStage.icon,
            progress: 0,
            status: 'pending',
            estimatedTime: delighterStage.estimatedTime || '5s'
          },
          ...stages.slice(idx)
        ];
      }
    }
  }
  return stages;
};

// Update getHunyuanStages to match actual logging output stages
const getHunyuanStages = (includeDelighter: boolean = false) => {
  const stages: ProgressStage[] = [
    {
      id: 'hunyuan_loading',
      name: 'Loading Pipeline',
      description: 'Initializing Hunyuan3D pipeline and models',
      icon: <Circle className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime: '30s'
    },
    {
      id: 'hunyuan_diffusion',
      name: 'Diffusion Sampling',
      description: 'Generating 3D structure through diffusion process',
      icon: <Cpu className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime: '300s'
    },
    {
      id: 'hunyuan_decoding',
      name: 'Volume Decoding',
      description: 'Converting diffusion output to 3D volume',
      icon: <Cpu className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime: '120s'
    },
    {
      id: 'hunyuan_texture',
      name: 'Texturing Mesh',
      description: 'Processing mesh and applying textures',
      icon: <Download className="w-4 h-4" />,
      progress: 0,
      status: 'pending',
      estimatedTime: '180s'
    }
  ];
  if (includeDelighter) {
    const delighterStage = defaultStages.find(s => s.id === 'texture_enhancement');
    if (delighterStage) {
      stages.push({ ...delighterStage });
    }
  }
  return stages;
};

// Replace getStagesForPipeline fallback to always require explicit pipeline
const getStagesForPipeline = (pipeline: 'trellis' | 'hunyuan', includeDelighter: boolean = false) => {
  if (pipeline === 'trellis') return getTrellisStages(includeDelighter);
  if (pipeline === 'hunyuan') return getHunyuanStages(includeDelighter);
  return [];
};

export const ProgressBar: React.FC<ProgressBarProps> = ({
  isVisible,
  isDarkMode,
  sessionId,
  generationMode = 'image-to-3d',
  delighterEnabled = false,
  onComplete,
  onError,
  pipeline
}) => {
  const electronProgressDetected = React.useRef(false);

  // Always initialize stages to the correct pipeline's stages
  const [stages, setStages] = useState<ProgressStage[]>(getStagesForPipeline(pipeline, delighterEnabled));
  const [detectedPipeline, setDetectedPipeline] = useState<'trellis' | 'hunyuan' | null>(pipeline);
  const [currentStageIndex, setCurrentStageIndex] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const lastSessionIdRef = React.useRef<string | undefined>(undefined);

  // Only reset and start simulation when a new sessionId is detected
  useEffect(() => {
    if (!isVisible || !sessionId) return;
    if (lastSessionIdRef.current === sessionId) return; // Already started for this session
    lastSessionIdRef.current = sessionId;
    setDetectedPipeline(pipeline); // Set to correct pipeline immediately
    setOverallProgress(0);
    setElapsedTime(0);
    setEstimatedTimeRemaining('');
    setStages(getStagesForPipeline(pipeline, delighterEnabled).map(stage => ({
      ...stage,
      progress: 0,
      status: 'pending' as const
    })));
    setCurrentStageIndex(0);
  }, [isVisible, sessionId, pipeline, delighterEnabled]);

  // Timer for elapsed time
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isVisible && currentStageIndex < stages.length) {
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isVisible, currentStageIndex, stages.length]);

  // Calculate overall progress
  useEffect(() => {
    const completedStages = stages.filter(stage => stage.status === 'completed').length;
    const activeStage = stages.find(stage => stage.status === 'active');
    const activeProgress = activeStage ? activeStage.progress : 0;

    const overall = ((completedStages + (activeProgress / 100)) / stages.length) * 100;
    setOverallProgress(Math.min(overall, 100));
  }, [stages]);

  // Disable SSE & polling if we are receiving electron progress
  const skipNetworkProgress = electronProgressDetected.current;

  // Calculate estimated time remaining
  useEffect(() => {
    const remainingStages = stages.slice(currentStageIndex);
    const totalEstimated = remainingStages.reduce((total, stage) => {
      const time = parseInt(stage.estimatedTime?.replace('s', '') || '0');
      return total + time;
    }, 0);

    const currentStageProgress = stages[currentStageIndex]?.progress || 0;
    const currentStageTime = parseInt(stages[currentStageIndex]?.estimatedTime?.replace('s', '') || '0');
    const currentStageRemaining = (currentStageTime * (100 - currentStageProgress)) / 100;

    const totalRemaining = Math.max(0, totalEstimated - currentStageTime + currentStageRemaining);
    setEstimatedTimeRemaining(totalRemaining > 0 ? `${Math.ceil(totalRemaining)}s` : '');
  }, [stages, currentStageIndex]);

  // Remove Trellis simulation – rely on real progress events
  useEffect(() => {
    // Intentionally empty: Trellis progress is driven by onPipelineStatus
    return () => {};
  }, [isVisible, detectedPipeline, delighterEnabled, sessionId]);

  // Remove Hunyuan simulation – rely on real progress events
  useEffect(() => {
    // Intentionally empty: Hunyuan progress is driven by onPipelineStatus
    return () => {};
  }, [isVisible, detectedPipeline, delighterEnabled, sessionId]);

  // Helper: Find index of stage by id
  const findStageIndex = (stagesArr: ProgressStage[], stageId: string) => stagesArr.findIndex(s => s.id === stageId);

  // Listen to Electron backend progress events as an alternative to SSE
  useEffect(() => {
    if (skipNetworkProgress || !isVisible || !sessionId) return;

    const removeListener = (window as any).electronAPI?.onPipelineStatus?.((status: any) => {
      if (!status || typeof status !== 'object') return;
      if (status.event !== 'progress') return;
      if (status.session_id !== sessionId) return;

      // Detect pipeline type based on stage prefix
      const currentPipeline = status.stage?.startsWith('hunyaun') || status.stage?.startsWith('hunyuan_') ? 'hunyuan' : 'trellis';
      if (detectedPipeline !== currentPipeline) {
        setDetectedPipeline(currentPipeline);
        setStages(currentPipeline === 'hunyuan' ? getHunyuanStages(delighterEnabled) : getTrellisStages(delighterEnabled));
      }

      // Update stages and currentStageIndex robustly
      setStages(prevStages => {
        let updatedStages = prevStages.map(stage => {
          if (stage.id === status.stage) {
            // Remove any Unicode/emoji from description
            let cleanDesc = (status.message || status.description || stage.description || '').replace(/[^\x00-\x7F]/g, '');
            if (status.step && status.total && status.step > 0) {
              cleanDesc += ` (${status.step}/${status.total})`;
            }
            const newProgress = Math.min(100, status.stage_progress || status.progress || 0);
            let newStatus: ProgressStage['status'] = newProgress >= 100 ? 'completed' : (newProgress > 0 ? 'active' : 'pending');
            return {
              ...stage,
              progress: newProgress,
              status: newStatus,
              description: cleanDesc,
            };
          }
          return stage;
        });

        // Advance currentStageIndex if needed
        const activeIdx = updatedStages.findIndex(s => s.status === 'active');
        const completedIdx = updatedStages.findIndex(s => s.status === 'completed');
        if (activeIdx !== -1) {
          setCurrentStageIndex(activeIdx);
        } else if (completedIdx !== -1) {
          // If all are completed, set to last
          setCurrentStageIndex(Math.max(completedIdx, 0));
        }
        return updatedStages;
      });

      // Update overall progress if available
      if (status.overall_progress !== undefined) {
        setOverallProgress(Math.min(100, status.overall_progress));
      }
    });

    return () => {
      if (removeListener) removeListener();
    };
  }, [skipNetworkProgress, isVisible, sessionId, generationMode, delighterEnabled]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
  };

  if (!isVisible) return null;

  const currentStage = stages[currentStageIndex];
  const currentStageName = currentStage?.name || 'Initializing...';

  return (
    <div className={`relative w-full rounded-lg border ${
      isDarkMode
        ? 'bg-gray-800 border-gray-700'
        : 'bg-white border-gray-200'
    } shadow-lg overflow-visible`}>
      {/* Compact Header - Always Visible */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Loader2 className={`w-5 h-5 animate-spin ${
              isDarkMode ? 'text-blue-400' : 'text-blue-600'
            }`} />
            <div className="flex flex-col">
              <h3 className={`text-sm font-semibold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Generating 3D Model
              </h3>
              <p className={`text-xs ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                {currentStageName}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-4 text-xs">
              <div className={`flex items-center gap-1 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                <Clock className="w-3 h-3" />
                <span>{formatTime(elapsedTime)}</span>
              </div>
              <span className={`font-medium ${
                isDarkMode ? 'text-blue-400' : 'text-blue-600'
              }`}>
                {Math.round(overallProgress)}%
              </span>
            </div>

            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className={`p-1 rounded hover:bg-opacity-20 transition-colors ${
                isDarkMode
                  ? 'text-gray-400 hover:bg-gray-600 hover:text-gray-300'
                  : 'text-gray-500 hover:bg-gray-200 hover:text-gray-700'
              }`}
              aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>

        {/* Compact Progress Bar */}
        <div className="mt-3">
          <div className={`w-full h-2 rounded-full ${
            isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
          }`}>
            <div
              className={`h-full rounded-full transition-all duration-300 ${
                isDarkMode
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600'
              }`}
              style={{ width: `${overallProgress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Expandable Details - Positioned absolutely to expand downward only */}
      {isExpanded && (
        <div className={`absolute top-full left-0 right-0 z-10 rounded-b-lg border-t-0 border ${
          isDarkMode
            ? 'bg-gray-800 border-gray-700'
            : 'bg-white border-gray-200'
        } shadow-lg`}>
          <div className={`border-t px-4 pb-4 ${
            isDarkMode ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="pt-4 space-y-3">
              {stages.map((stage, index) => (
                <div key={stage.id} className="flex items-center gap-3">
                  {/* Status Icon */}
                  <div className={`flex-shrink-0 ${
                    stage.status === 'completed' ? (isDarkMode ? 'text-green-400' : 'text-green-600') :
                    stage.status === 'active' ? (isDarkMode ? 'text-blue-400' : 'text-blue-600') :
                    stage.status === 'error' ? (isDarkMode ? 'text-red-400' : 'text-red-600') :
                    (isDarkMode ? 'text-gray-500' : 'text-gray-400')
                  }`}>
                    {stage.status === 'completed' ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : stage.status === 'active' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <div className="w-4 h-4 flex items-center justify-center">
                        {React.cloneElement(stage.icon as React.ReactElement, { className: 'w-3 h-3' })}
                      </div>
                    )}
                  </div>

                  {/* Stage Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className={`text-sm font-medium ${
                        stage.status === 'active' ? (isDarkMode ? 'text-white' : 'text-gray-900') :
                        stage.status === 'completed' ? (isDarkMode ? 'text-green-400' : 'text-green-600') :
                        (isDarkMode ? 'text-gray-400' : 'text-gray-500')
                      }`}>
                        {stage.name}
                      </h4>
                      {stage.status === 'active' && (
                        <span className={`text-xs font-medium ${
                          isDarkMode ? 'text-blue-400' : 'text-blue-600'
                        }`}>
                          {Math.round(stage.progress)}%
                        </span>
                      )}
                    </div>
                    <p className={`text-xs ${
                      isDarkMode ? 'text-gray-500' : 'text-gray-400'
                    }`}>
                      {stage.description}
                    </p>

                    {/* Stage Progress Bar */}
                    {stage.status === 'active' && (
                      <div className={`mt-2 w-full h-1.5 rounded-full ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      }`}>
                        <div
                          className={`h-full rounded-full transition-all duration-200 ${
                            isDarkMode ? 'bg-blue-500' : 'bg-blue-600'
                          }`}
                          style={{ width: `${stage.progress}%` }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {estimatedTimeRemaining && (
              <div className={`mt-4 pt-3 border-t text-center text-xs ${
                isDarkMode ? 'border-gray-700 text-gray-400' : 'border-gray-200 text-gray-500'
              }`}>
                Estimated time remaining: {estimatedTimeRemaining}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
