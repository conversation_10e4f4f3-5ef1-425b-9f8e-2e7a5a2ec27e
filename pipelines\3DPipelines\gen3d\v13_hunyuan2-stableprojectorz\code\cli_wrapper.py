#!/usr/bin/env python3
"""
CLI Wrapper for Hunyuan3D Pipeline
This script provides a command-line interface for the Hunyuan3D 3D generation pipeline
with proper progress tracking for integration with StableProjectorz.
"""

import sys
import os
import json
import argparse
import time
import traceback
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def emit_progress(stage, progress, step=None, total=None, message="", timestamp=None):
    """Emit progress update in JSON format for Node.js backend to parse"""
    if timestamp is None:
        timestamp = time.time()

    # Calculate overall progress based on stage weights
    stage_weights = {
        'hunyuan_loading': 10,      # 10% of total
        'hunyuan_diffusion': 40,    # 40% of total
        'hunyuan_decoding': 30,     # 30% of total
        'hunyuan_texture': 20       # 20% of total
    }

    # Calculate cumulative progress
    stage_order = ['hunyuan_loading', 'hunyuan_diffusion', 'hunyuan_decoding', 'hunyuan_texture']
    current_stage_idx = stage_order.index(stage) if stage in stage_order else 0

    overall_progress = 0
    for i, stage_name in enumerate(stage_order):
        weight = stage_weights.get(stage_name, 25)
        if i < current_stage_idx:
            # Previous stages are complete
            overall_progress += weight
        elif i == current_stage_idx:
            # Current stage contributes partial progress
            overall_progress += (progress / 100) * weight
        # Future stages contribute 0

    progress_data = {
        "stage": stage,
        "progress": min(100, max(0, progress)),
        "step": step or 1,
        "total": total or 1,
        "stage_progress": min(100, max(0, progress)),
        "overall_progress": min(100, max(0, overall_progress)),
        "message": message,
        "timestamp": timestamp
    }

    # Emit as JSON that Node.js can parse
    print(f"PROGRESS:{json.dumps(progress_data)}", flush=True)

def main():
    parser = argparse.ArgumentParser(description="Hunyuan3D CLI Wrapper")
    parser.add_argument("image_path", help="Path to input image")
    parser.add_argument("output_path", help="Path to output GLB file")
    parser.add_argument("settings", help="JSON settings string")
    
    args = parser.parse_args()
    
    try:
        # Parse settings
        settings = json.loads(args.settings) if args.settings else {}
        
        # Import required modules
        emit_progress("hunyuan_preprocessing", 5, message="Loading Hunyuan3D pipeline...")
        
        import torch
        from PIL import Image
        from hy3dgen.shapegen import Hunyuan3DDiTFlowMatchingPipeline
        from hy3dgen.texgen import Hunyuan3DPaintPipeline
        from hy3dgen.rembg import BackgroundRemover
        
        emit_progress("hunyuan_preprocessing", 10, message="Initializing models...")
        
        # Initialize pipeline
        model_path = settings.get('model_path', 'tencent/Hunyuan3D-2mini')
        subfolder = settings.get('subfolder', 'hunyuan3d-dit-v2-mini-turbo')
        device = settings.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load shape generation pipeline
        shape_pipeline = Hunyuan3DDiTFlowMatchingPipeline.from_pretrained(
            model_path,
            subfolder=subfolder,
            use_safetensors=True,
            device=device,
        )
        
        emit_progress("hunyuan_preprocessing", 15, message="Loading background remover...")
        
        # Load background remover
        rembg = BackgroundRemover()
        
        emit_progress("hunyuan_preprocessing", 20, message="Loading input image...")
        
        # Load and preprocess image
        image = Image.open(args.image_path)
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        # Remove background if needed
        if settings.get('remove_background', True):
            emit_progress("hunyuan_preprocessing", 25, message="Removing background...")
            image = rembg(image)
        
        emit_progress("hunyuan_preprocessing", 30, message="Image preprocessing complete")
        
        # Generate 3D structure
        emit_progress("hunyuan_generation", 0, message="Starting 3D structure generation...")
        
        # Set up generation parameters
        num_inference_steps = settings.get('num_inference_steps', 20)
        guidance_scale = settings.get('guidance_scale', 5.5)
        seed = settings.get('seed', 42)
        
        # Create progress callback for shape generation
        def shape_progress_callback(step, total_steps, latents=None):
            progress = int((step / total_steps) * 100)
            emit_progress("hunyuan_generation", progress, step, total_steps, 
                         f"Diffusion step {step}/{total_steps}")
        
        # Generate shape
        generator = torch.Generator(device=device).manual_seed(seed)
        
        # Run shape generation with progress tracking
        outputs = shape_pipeline(
            image,
            num_inference_steps=num_inference_steps,
            guidance_scale=guidance_scale,
            generator=generator,
            callback=shape_progress_callback,
            callback_steps=1
        )
        
        emit_progress("hunyuan_generation", 100, message="3D structure generation complete")
        
        # Export to GLB
        emit_progress("hunyuan_export", 0, message="Starting GLB export...")
        
        # Get the mesh from outputs
        mesh = outputs[0] if isinstance(outputs, (list, tuple)) else outputs
        
        emit_progress("hunyuan_export", 30, message="Processing mesh...")
        
        # Apply texture if requested
        if settings.get('apply_texture', False):
            emit_progress("hunyuan_export", 40, message="Applying texture...")
            
            texgen_model_path = settings.get('texgen_model_path', 'tencent/Hunyuan3D-2')
            texture_pipeline = Hunyuan3DPaintPipeline.from_pretrained(
                texgen_model_path,
                device=device
            )
            
            # Apply texture with progress tracking
            def texture_progress_callback(step, total_steps, latents=None):
                progress = 40 + int((step / total_steps) * 40)  # 40-80% range
                emit_progress("hunyuan_export", progress, step, total_steps,
                             f"Texture generation step {step}/{total_steps}")
            
            mesh = texture_pipeline(
                mesh, 
                image,
                callback=texture_progress_callback,
                callback_steps=1
            )
            
            emit_progress("hunyuan_export", 80, message="Texture application complete")
        
        emit_progress("hunyuan_export", 90, message="Saving GLB file...")
        
        # Save the mesh
        output_dir = os.path.dirname(args.output_path)
        os.makedirs(output_dir, exist_ok=True)
        
        # Export mesh to GLB format
        if hasattr(mesh, 'export'):
            mesh.export(args.output_path)
        else:
            # Convert to trimesh if needed
            import trimesh
            if hasattr(mesh, 'vertices') and hasattr(mesh, 'faces'):
                trimesh_obj = trimesh.Trimesh(vertices=mesh.vertices, faces=mesh.faces)
                trimesh_obj.export(args.output_path)
            else:
                raise ValueError("Unable to export mesh - unknown format")
        
        emit_progress("hunyuan_export", 100, message="GLB export complete")
        
        # Final success message
        result = {
            "success": True,
            "message": "3D model generated successfully",
            "output_path": args.output_path
        }
        print(f"RESULT:{json.dumps(result)}", flush=True)
        
    except Exception as e:
        error_msg = f"Error in Hunyuan3D generation: {str(e)}"
        print(f"ERROR:{error_msg}", file=sys.stderr, flush=True)
        traceback.print_exc()
        
        result = {
            "success": False,
            "error": error_msg
        }
        print(f"RESULT:{json.dumps(result)}", flush=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
