========================================
AIStudio Real-time Log: hunyuan3d
Started: 2025-08-08T19:42:12.198Z
File: process_hunyuan3d_2025-08-08_14-42-12_001.log
========================================

[2025-08-08T19:42:12.197Z] [STDOUT] Using AIStudio models directory: M:\AIStudio\models
[2025-08-08T19:42:12.283Z] [STDOUT] M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.
[2025-08-08T19:42:12.283Z] [STDOUT] "M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe"
[2025-08-08T19:42:12.283Z] [STDOUT] "M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\get-pip.py"
[2025-08-08T19:42:18.143Z] [STDOUT] Creating virtual environment using portable Python...
[2025-08-08T19:42:18.144Z] [STDOUT] "M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe" -m virtualenv "M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv"
[2025-08-08T19:42:31.863Z] [STDOUT] sitecustomize.py applied
created virtual environment CPython3.11.9.final.0-64 in 11698ms
  creator CPython3Windows(dest=M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv, clear=False, no_vcs_ignore=False, global=False)
  seeder FromAppData(download=False, pip=bundle, setuptools=bundle, via=copy, app_data_dir=C:\Users\<USER>\AppData\Local\pypa\virtualenv)
    added seed packages: Deprecated==1.2.18, MarkupSafe==2.1.5, PyMatting==1.1.13, PyYAML==6.0.2, accelerate==1.5.2, aiofiles==23.2.1, annotated_types==0.7.0, antlr4_python3_runtime==4.9.3, anyio==4.9.0, attrs==25.3.0, certifi==2022.12.7, charset_normalizer==2.1.1, click==8.1.8, colorama==0.4.6, coloredlogs==15.0.1, contourpy==1.3.1, custom_rasterizer==0.1, cycler==0.12.1, dataclasses_json==0.6.7, diffusers==0.32.2, einops==0.8.1, fastapi==0.115.6, ffmpy==0.5.0, filelock==3.13.1, flatbuffers==25.2.10, fonttools==4.56.0, fsspec==2024.6.1, gradio==4.44.1, gradio_client==1.3.0, gradio_litmodel3d==0.0.1, groovy==0.1.2, h11==0.14.0, httpcore==1.0.7, httpx==0.28.1, huggingface_hub==0.29.3, humanfriendly==10.0, idna==3.4, imageio==2.37.0, importlib_metadata==8.6.1, importlib_resources==6.5.2, jinja2==3.1.4, jsonschema==4.23.0, jsonschema_specifications==2024.10.1, kiwisolver==1.4.8, lazy_loader==0.4, llvmlite==0.44.0, markdown_it_py==3.0.0, marshmallow==3.26.1, matplotlib==3.10.1, mdurl==0.1.2, mesh_processor==0.1.0, mpmath==1.3.0, msvc_runtime==14.42.34433, mypy_extensions==1.0.0, networkx==3.3, ninja==********, numba==0.61.0, numpy==1.25.2, omegaconf==2.3.0, onnxruntime==1.21.0, opencv_python==*********, opencv_python_headless==*********, orjson==3.10.16, packaging==24.2, pandas==2.2.3, pillow==10.4.0, pip==25.1.1, platformdirs==4.3.7, pooch==1.8.2, protobuf==6.30.2, psutil==7.0.0, pybind11==2.13.6, pydantic==2.10.5, pydantic_core==2.27.2, pydub==0.25.1, pygltflib==1.16.3, pygments==2.19.1, pymeshlab==2023.12.post3, pyparsing==3.2.3, pyreadline3==3.5.4, python_dateutil==2.9.0.post0, python_multipart==0.0.20, pytz==2025.2, referencing==0.36.2, regex==2024.11.6, rembg==2.0.65, requests==2.32.3, rich==13.9.4, rpds_py==0.24.0, ruff==0.11.2, safehttpx==0.1.6, safetensors==0.5.3, scikit_image==0.25.2, scipy==1.15.2, semantic_version==2.10.0, setuptools==80.9.0, shellingham==1.5.4, six==1.17.0, sniffio==1.3.1, starlette==0.41.3, sympy==1.13.1, tifffile==2025.3.13, tokenizers==0.21.1, tomlkit==0.12.0, torch==2.5.1+cu124, torchaudio==2.5.1+cu124, torchvision==0.20.1+cu124, tqdm==4.67.1, transformers==4.50.3, trimesh==4.6.5, typer==0.15.2, typing_extensions==4.12.2, typing_inspect==0.9.0, typing_inspection==0.4.0, tzdata==2025.2, urllib3==2.3.0, uvicorn==0.34.0, websockets==12.0, wrapt==1.17.2, xatlas==0.0.9, zipp==3.21.0
  activators BashActivator,BatchActivator,FishActivator,NushellActivator,PowerShellActivator,PythonActivator
[2025-08-08T19:42:32.826Z] [STDOUT] 1 file(s) copied.
[2025-08-08T19:42:33.062Z] [STDOUT] Portable Python located at: M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\python\python.exe
[2025-08-08T19:42:33.062Z] [STDOUT] Virtual environment Python set to: "M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-08T19:42:33.063Z] [STDOUT] _
[2025-08-08T19:42:33.063Z] [STDOUT] Current Python: "M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-08T19:42:33.064Z] [STDOUT] Virtual Env: M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-08T19:42:33.065Z] [STDOUT] _
[2025-08-08T19:42:33.068Z] [STDOUT] Current Python: "M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\tools\.\..\code\venv\Scripts\python.exe"
[2025-08-08T19:42:33.069Z] [STDOUT] Virtual Env: M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv
[2025-08-08T19:42:33.069Z] [STDOUT] Starting the server, please wait...
[2025-08-08T19:44:45.649Z] [STDERR] M:\AIStudio\pipelines\3DPipelines\gen3d\v13_hunyuan2-stableprojectorz\code\venv\Lib\site-packages\transformers\utils\hub.py:105: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
[2025-08-08T19:45:52.262Z] [STDOUT] Loading example img list ...
Loading example txt list ...
Loading example mv list ...
[2025-08-08T19:45:52.263Z] [STDERR] Fetching 13 files:   0%|          | 0/13 [00:00<?, ?it/s]
[2025-08-08T19:45:52.264Z] [STDERR] Fetching 13 files: 100%|##########| 13/13 [00:00<?, ?it/s]
[2025-08-08T19:45:52.381Z] [STDERR] Fetching 17 files:   0%|          | 0/17 [00:00<?, ?it/s]
[2025-08-08T19:45:52.383Z] [STDERR] Fetching 17 files: 100%|##########| 17/17 [00:00<?, ?it/s]
[2025-08-08T19:45:53.326Z] [STDERR] Loading pipeline components...:   0%|          | 0/6 [00:00<?, ?it/s]
[2025-08-08T19:45:54.457Z] [STDERR] Loading pipeline components...:  17%|#6        | 1/6 [00:01<00:05,  1.13s/it]
[2025-08-08T19:45:55.543Z] [STDERR] Loading pipeline components...:  50%|#####     | 3/6 [00:02<00:02,  1.43it/s]
